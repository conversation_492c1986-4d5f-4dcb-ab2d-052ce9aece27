<template>
  <div v-for="(group, inx) in data" :key="inx">
    <!-- 标题 -->
    <h3 class="group_title">{{ group.title }}</h3>
    <!-- 统计数据层 -->
    <div class="card_con" v-if="group.statisticsConfig || group.statisticsList">
      <!-- 新版本：使用statisticsConfig + 上下文系统 -->
      <template v-if="group.statisticsConfig">
        <div class="item" v-for="item in getStatisticsItems(group)" :key="item.id">
          <p class="car_title">{{ item.title }}</p>
          <a-tag color="blue" class="con_r" v-if="item.core">{{ t('coreIndicators') }}</a-tag>

          <!-- 🔥 加载状态：使用上下文系统的状态 -->
          <div v-if="item.loading || getStatisticsLoadingState(group)" class="loading-placeholder">
            <a-skeleton-input :active="true" size="large" />
          </div>
          <h3 v-else>{{ item.total || '--' }}</h3>

          <div class="d_f">
            <span>{{ t('chain') }}:</span>
            <a-tag class="tag flex items-center" :color="item.increaseTrend ? 'green' : 'red'">
              <Icon :icon="item.increaseTrend ? 'ant-design:arrow-up-outlined' : 'ant-design:arrow-down-outlined'" />
              {{ `${item.increase || 0}%` }}
            </a-tag>
          </div>
          <div class="d_f">
            <span>{{ t('onYear') }}:</span>
            <!-- 🔥 兜底处理：当同比数据不存在时显示 -- -->
            <a-tag class="tag flex items-center" :color="item.decrease !== undefined ? (item.decreaseTrend ? 'green' : 'red') : 'default'">
              <Icon
                v-if="item.decrease !== undefined"
                :icon="item.decreaseTrend ? 'ant-design:arrow-up-outlined' : 'ant-design:arrow-down-outlined'"
              />
              {{ item.decrease !== undefined ? `${item.decrease}%` : '--' }}
            </a-tag>
          </div>
        </div>
      </template>

      <!-- 兼容旧版本：使用statisticsList -->
      <template v-else-if="group.statisticsList">
        <div class="item" v-for="(item, i) in group.statisticsList" :key="i">
          <p class="car_title">{{ item.title }}</p>
          <a-tag color="blue" class="con_r" v-if="item.core">{{ t('coreIndicators') }}</a-tag>
          <h3>{{ item.total || '--' }}</h3>
          <div class="d_f">
            <span>{{ t('chain') }}:</span>
            <a-tag class="tag flex items-center" color="green"><Icon icon="ant-design:arrow-up-outlined" />{{ `${item.increase || 0}%` }}</a-tag>
          </div>
          <div class="d_f">
            <span>{{ t('onYear') }}:</span>
            <a-tag class="tag flex items-center" color="red"><Icon icon="ant-design:arrow-down-outlined" />{{ `${item.decrease || 0}%` }}</a-tag>
          </div>
        </div>
      </template>
    </div>

    <!-- 局部筛选区层 todo -->

    <!-- 图表数据层 -->
    <div class="chart-grid" :style="gridStyle" v-if="group.chartList">
      <div v-for="chart in group.chartList" :key="chart.id">
        <div class="chart-item" :data-chart-id="chart.id">
          <ChartGroup :chart-id="chart.id" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
  export default {
    name: 'ChartGroupContainer',
  };
</script>
<script setup lang="ts">
  // import { computed } from 'vue'; // 暂时不需要
  import { Icon } from '/@/components/Icon';
  import type { TabConfigGroupConfig } from '../types/statisticDashboard';
  import ChartGroup from './ChartGroup.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useStatisticsDataOptional, useStatisticsStateOptional } from '../hooks/useStatisticsActions';

  const { t } = useI18n('common');

  // 🔥 使用统计数据上下文系统
  const statisticsData = useStatisticsDataOptional();
  const statisticsState = useStatisticsStateOptional();

  // Props定义
  interface Props {
    // 排版样式
    gridStyle: any;
    /** 分组数据（包含统计数据，图表数据） */
    data: TabConfigGroupConfig[];
  }

  const _props = defineProps<Props>(); // 暂时不直接使用props

  // 🔥 计算属性：获取实时的统计数据
  const getStatisticsItems = (group: TabConfigGroupConfig) => {
    console.log(`🔍 getStatisticsItems: 开始获取统计数据`, group);

    if (group.statisticsConfig) {
      console.log(`🔍 getStatisticsItems: 使用statisticsConfig模式, configId: ${group.statisticsConfig.id}`);

      // 从上下文系统获取最新的统计数据
      const contextData = statisticsData.getStatisticsData(group.statisticsConfig.id);
      console.log(`🔍 getStatisticsItems: 上下文返回数据:`, contextData);

      if (contextData && contextData.length > 0) {
        console.log(`✅ getStatisticsItems: 从上下文获取统计数据: ${group.statisticsConfig.id}, 数量: ${contextData.length}`);
        return contextData;
      }

      // 如果上下文没有数据，使用配置中的默认数据
      console.log(`📋 getStatisticsItems: 使用配置默认数据: ${group.statisticsConfig.id}, 数量: ${group.statisticsConfig.items.length}`);
      console.log(`📋 getStatisticsItems: 配置默认数据详情:`, group.statisticsConfig.items);
      return group.statisticsConfig.items;
    }

    // 兼容旧版本
    console.log(`🔄 getStatisticsItems: 使用兼容模式 statisticsList`);
    return group.statisticsList || [];
  };

  // 🔥 计算属性：获取统计配置的加载状态
  const getStatisticsLoadingState = (group: TabConfigGroupConfig) => {
    if (group.statisticsConfig) {
      return statisticsState.statisticsLoadingStates[group.statisticsConfig.id] || false;
    }
    return false;
  };

  // 注意：图表数据获取现在由 ChartGroup 组件通过上下文系统直接处理
  // 统计数据现在也通过上下文系统获取，实现了统一的数据管理
</script>
<style lang="less" scoped>
  .group_title {
    margin-left: 30px;
  }
  .card_con {
    display: flex;
    flex-wrap: wrap;
    gap: 80px;
    padding: 20px;
    .item {
      background: #fff;
      border-radius: 20px;
      position: relative;
      box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
      min-width: 250px;
      padding: 10px;
      .car_title {
        font-size: 14px;
        font-weight: 600;
      }
      h3 {
        font-size: 22px;
        font-weight: 600;
      }
      .con_r {
        position: absolute;
        right: 0px;
        top: 10px;
        border-radius: 10px;
      }
      .d_f {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        .tag {
          border-radius: 10px;
        }
      }
    }
  }
</style>
