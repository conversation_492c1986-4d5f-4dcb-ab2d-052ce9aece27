<template>
  <div v-for="(group, inx) in data" :key="inx">
    <!-- 标题 -->
    <h3 class="group_title">{{ group.title }}</h3>
    <!-- 统计数据层 -->
    <div class="card_con" v-if="group.statisticsConfig">
      <div class="item" v-for="item in getStatisticsItems(group)" :key="item.id">
        <p class="car_title">{{ item.title }}</p>
        <a-tag color="blue" class="con_r" v-if="item.core">{{ t('coreIndicators') }}</a-tag>

        <div v-if="item.loading || getStatisticsLoadingState(group)" class="loading-placeholder">
          <a-skeleton-input :active="true" size="large" />
        </div>
        <h3 v-else>{{ item.total }}</h3>

        <div class="d_f">
          <span>{{ t('chain') }}:</span>
          <a-tag class="tag flex items-center" :color="item.increaseTrend ? 'green' : 'red'">
            <Icon :icon="item.increaseTrend ? 'ant-design:arrow-up-outlined' : 'ant-design:arrow-down-outlined'" />
            {{ `${item.increase || 0}%` }}
          </a-tag>
        </div>
        <div class="d_f">
          <span>{{ t('onYear') }}:</span>
          <a-tag class="tag flex items-center" :color="item.decrease !== undefined ? (item.decreaseTrend ? 'green' : 'red') : 'default'">
            <Icon v-if="item.decrease !== undefined" :icon="item.decreaseTrend ? 'ant-design:arrow-up-outlined' : 'ant-design:arrow-down-outlined'" />
            {{ item.decrease !== undefined ? `${item.decrease}%` : '--' }}
          </a-tag>
        </div>
      </div>
    </div>

    <!-- 局部筛选区层 todo -->

    <!-- 图表数据层 -->
    <div class="chart-grid" :style="gridStyle" v-if="group.chartList">
      <div v-for="chart in group.chartList" :key="chart.id">
        <div class="chart-item" :data-chart-id="chart.id">
          <ChartGroup :chart-id="chart.id" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
  export default {
    name: 'ChartGroupContainer',
  };
</script>
<script setup lang="ts">
  import { Icon } from '/@/components/Icon';
  import type { TabConfigGroupConfig } from '../types/statisticDashboard';
  import ChartGroup from './ChartGroup.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useStatisticsDataOptional, useStatisticsActionsOptional } from '../hooks/useStatisticsActions';

  const { t } = useI18n('common');

  const statisticsData = useStatisticsDataOptional();
  const statisticsActions = useStatisticsActionsOptional();

  interface Props {
    gridStyle: any;
    data: TabConfigGroupConfig[];
  }

  defineProps<Props>();

  const getStatisticsItems = (group: TabConfigGroupConfig) => {
    if (group.statisticsConfig) {
      const contextData = statisticsData.getStatisticsData(group.statisticsConfig.id);

      if (contextData && contextData.length > 0) {
        return contextData;
      }

      return group.statisticsConfig.items;
    }

    return [];
  };

  const getStatisticsLoadingState = (group: TabConfigGroupConfig) => {
    if (group.statisticsConfig) {
      return statisticsActions.statisticsLoadingStates[group.statisticsConfig.id] || false;
    }
    return false;
  };
</script>
<style lang="less" scoped>
  .group_title {
    margin-left: 30px;
  }
  .card_con {
    display: flex;
    flex-wrap: wrap;
    gap: 80px;
    padding: 20px;
    .item {
      background: #fff;
      border-radius: 20px;
      position: relative;
      box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
      min-width: 250px;
      padding: 10px;
      .car_title {
        font-size: 14px;
        font-weight: 600;
      }
      h3 {
        font-size: 22px;
        font-weight: 600;
      }
      .con_r {
        position: absolute;
        right: 0px;
        top: 10px;
        border-radius: 10px;
      }
      .d_f {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        .tag {
          border-radius: 10px;
        }
      }
    }
  }
</style>
