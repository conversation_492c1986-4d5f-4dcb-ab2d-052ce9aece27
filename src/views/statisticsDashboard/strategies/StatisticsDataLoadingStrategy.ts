/**
 * 统计数据加载策略系统
 * 基于策略模式实现统计数据的统一加载和管理
 */

import { reactive } from 'vue';
import type { StatisticsItem, StatisticsConfig } from '../types/statisticDashboard';
import type { commonQueryParams, ClueOverViewResponse } from '../api/index';
import { getCurrentQueryParams } from '../hooks/useQueryParamsContext';

// ========== 接口定义 ==========

/**
 * 统计数据加载策略接口
 */
export interface IStatisticsDataLoadingStrategy {
  /** 策略类型标识 */
  readonly strategyType: string;
  /** 支持的数据源类型 */
  readonly supportedDataSources: string[];

  /**
   * 加载统计数据
   * @param config 统计配置
   * @param params 查询参数
   */
  loadStatisticsData(config: StatisticsConfig, params?: commonQueryParams): Promise<StatisticsItem[]>;

  /**
   * 刷新统计数据
   * @param config 统计配置
   * @param params 查询参数
   */
  refreshStatisticsData(config: StatisticsConfig, params?: commonQueryParams): Promise<StatisticsItem[]>;
}

/**
 * API装饰器接口 - 自动注入查询参数
 */
export interface IStatisticsApiDecorator {
  /**
   * 装饰API调用，自动注入查询参数
   */
  decorateApiCall<T>(apiCall: (params: commonQueryParams) => Promise<T>, customParams?: Partial<commonQueryParams>): Promise<T>;
}

// ========== API装饰器实现 ==========

/**
 * 统计数据API装饰器 - 自动注入筛选参数
 */
export class StatisticsApiDecorator implements IStatisticsApiDecorator {
  async decorateApiCall<T>(apiCall: (params: commonQueryParams) => Promise<T>, customParams?: Partial<commonQueryParams>): Promise<T> {
    try {
      // 🔥 自动获取当前筛选参数
      const currentParams = getCurrentQueryParams();

      // 合并参数：自定义参数 > 当前筛选参数
      const finalParams = {
        ...currentParams,
        ...customParams,
      } as commonQueryParams;

      console.log('🚀 统计数据API装饰器 - 最终参数:', finalParams);

      // 调用API
      return await apiCall(finalParams);
    } catch (error) {
      console.error('统计数据API调用失败:', error);
      throw error;
    }
  }
}

// ========== 抽象基类 ==========

/**
 * 抽象基类 - 统计数据加载策略模板
 */
export abstract class BaseStatisticsDataLoadingStrategy implements IStatisticsDataLoadingStrategy {
  protected apiDecorator: IStatisticsApiDecorator;
  protected loadingStates = reactive<Record<string, boolean>>({});

  constructor() {
    this.apiDecorator = new StatisticsApiDecorator();
  }

  // 抽象属性 - 子类必须实现
  abstract readonly strategyType: string;
  abstract readonly supportedDataSources: string[];

  // 抽象方法 - 子类必须实现
  protected abstract fetchApiData(params: commonQueryParams): Promise<any>;
  protected abstract transformData(apiData: any, config: StatisticsConfig): Promise<StatisticsItem[]>;

  /**
   * 加载统计数据 - 模板方法
   */
  async loadStatisticsData(config: StatisticsConfig, params?: commonQueryParams): Promise<StatisticsItem[]> {
    const configId = config.id;

    try {
      console.log(`🚀 开始加载统计数据 [${this.strategyType}]: ${configId}`);
      this.loadingStates[configId] = true;

      // 🔥 使用装饰器自动注入参数
      const apiData = await this.apiDecorator.decorateApiCall((finalParams) => this.fetchApiData(finalParams), params);

      // 转换数据
      const transformedData = await this.transformData(apiData, config);

      console.log(`✅ 统计数据加载成功: ${configId}, 数据条数: ${transformedData.length}`);
      return transformedData;
    } catch (error) {
      console.error(`❌ 统计数据加载失败: ${configId}`, error);
      throw error;
    } finally {
      this.loadingStates[configId] = false;
    }
  }

  /**
   * 刷新统计数据
   */
  async refreshStatisticsData(config: StatisticsConfig, params?: commonQueryParams): Promise<StatisticsItem[]> {
    console.log(`🔄 刷新统计数据: ${config.id}`);
    return this.loadStatisticsData(config, params);
  }

  /**
   * 获取加载状态
   */
  isLoading(configId: string): boolean {
    return this.loadingStates[configId] || false;
  }
}

// ========== 具体策略实现 ==========

/**
 * 线索总览统计数据加载策略
 */
export class ClueOverviewStatisticsStrategy extends BaseStatisticsDataLoadingStrategy {
  readonly strategyType = 'clue-overview';
  readonly supportedDataSources = ['clueOverview', 'clue-overview'];

  protected async fetchApiData(params: commonQueryParams): Promise<ClueOverViewResponse> {
    // 🔥 动态导入API，避免循环依赖
    const { queryClueOverView } = await import('../api/index');
    return await queryClueOverView(params);
  }

  protected async transformData(apiData: ClueOverViewResponse, config: StatisticsConfig): Promise<StatisticsItem[]> {
    console.log('🔄 转换线索总览统计数据:', apiData);

    // 定义API字段到统计项的映射
    const fieldMapping: Record<string, keyof ClueOverViewResponse> = {
      clueAllCount: 'clueAllCount', // 全量线索总量
      clueValidCount: 'clueValidCount', // 有效线索总量
      clueDealCount: 'clueDealCount', // 线索成交量
      clueFollowPercent: 'clueFollowPercent', // 线索跟进率
      clueValidPercent: 'clueValidPercent', // 线索有效率
      clueWinPercent: 'clueWinPercent', // 线索战胜率
    };

    const transformedItems: StatisticsItem[] = [];

    // 遍历配置中的统计项
    for (const item of config.items) {
      if (!item.id) continue; // 跳过没有ID的项
      const apiField = fieldMapping[item.id] as keyof ClueOverViewResponse;

      if (apiField && apiData[apiField]) {
        const overviewData = apiData[apiField];

        // 🔥 兜底处理：某些字段可能不存在
        const count = overviewData.count ?? 0;
        const ringRatio = overviewData.ringRatio ?? 0;
        const ringRatioTrend = overviewData.ringRatioTrend ?? true;

        // 🔥 特殊处理：同比数据可能完全不存在
        const hasYearData = overviewData.hasOwnProperty('yearRatio') || overviewData.hasOwnProperty('yearRatioTrend');
        const yearRatio = hasYearData ? (overviewData.yearRatio ?? 0) : undefined;
        const yearRatioTrend = hasYearData ? (overviewData.yearRatioTrend ?? true) : undefined;

        // 转换数据格式
        const transformedItem: StatisticsItem = {
          ...item, // 保留原有配置
          total: count,
          increase: Math.abs(ringRatio),
          increaseTrend: ringRatioTrend,
          decrease: yearRatio !== undefined ? Math.abs(yearRatio) : undefined,
          decreaseTrend: yearRatioTrend,
          loading: false,
        };

        console.log(`✅ 转换统计项 ${item.id}:`, {
          count,
          ringRatio,
          ringRatioTrend,
          yearRatio: yearRatio !== undefined ? yearRatio : '无同比数据',
          yearRatioTrend: yearRatioTrend !== undefined ? yearRatioTrend : '无同比趋势',
          hasYearData,
        });

        transformedItems.push(transformedItem);
      } else {
        // 如果没有对应的API数据，使用兜底数据
        console.warn(`⚠️ 未找到统计项 ${item.id} 对应的API数据，使用兜底数据`);
        transformedItems.push({
          ...item,
          total: 0,
          increase: 0,
          increaseTrend: true,
          decrease: undefined, // 🔥 无API数据时，同比数据设为undefined
          decreaseTrend: undefined,
          loading: false,
        });
      }
    }

    return transformedItems;
  }
}

// ========== 策略工厂 ==========

/**
 * 统计数据加载策略工厂
 */
export class StatisticsDataLoadingStrategyFactory {
  private static strategies: Map<string, IStatisticsDataLoadingStrategy> = new Map();

  /**
   * 注册策略
   */
  static registerStrategy(strategy: IStatisticsDataLoadingStrategy): void {
    this.strategies.set(strategy.strategyType, strategy);
    console.log(`📝 注册统计数据加载策略: ${strategy.strategyType}`);
  }

  /**
   * 获取策略
   */
  static getStrategy(config: StatisticsConfig): IStatisticsDataLoadingStrategy | null {
    console.log(`🔍 查找统计数据加载策略 - 配置ID: ${config.id}, 数据源: ${config.dataSource}`);

    // 1. 优先使用配置中明确指定的策略
    if (config.loadingStrategy && this.strategies.has(config.loadingStrategy)) {
      console.log(`🎯 使用指定策略: ${config.loadingStrategy}`);
      return this.strategies.get(config.loadingStrategy)!;
    }

    // 2. 根据数据源匹配策略
    for (const [strategyType, strategy] of this.strategies) {
      if (strategy.supportedDataSources.includes(config.dataSource)) {
        console.log(`🎯 根据数据源匹配策略: ${strategyType} for ${config.dataSource}`);
        return strategy;
      }
    }

    console.warn(`⚠️ 未找到合适的统计数据加载策略: ${config.id}, 数据源: ${config.dataSource}`);
    return null;
  }

  /**
   * 获取所有已注册的策略
   */
  static getAllStrategies(): IStatisticsDataLoadingStrategy[] {
    return Array.from(this.strategies.values());
  }

  /**
   * 清除所有策略（用于测试）
   */
  static clearStrategies(): void {
    this.strategies.clear();
  }
}

// ========== 自动注册策略 ==========

// 🔥 自动注册线索总览统计策略
StatisticsDataLoadingStrategyFactory.registerStrategy(new ClueOverviewStatisticsStrategy());

console.log('📊 统计数据加载策略系统初始化完成');
