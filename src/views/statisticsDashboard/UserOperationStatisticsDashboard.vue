<template>
  <div class="statistics-dashboard">
    <PageWrapper>
      <!-- <template #extra>
        <div class="flex items-center gap-2">
          <PerformanceMonitor />
          <a-button type="primary" @click="handleRefreshAll">
            <Icon icon="ant-design:reload-outlined" />
            刷新全部
          </a-button>
        </div>
      </template> -->

      <!-- 筛选器面板 - 集成吸顶功能 -->
      <StickyFilterWrapper
        ref="stickyFilterRef"
        :debug="true"
        :show-sticky-indicator="true"
        :extra-top-offset="0"
        :trigger-threshold="10"
        header-selector=".ant-layout-header"
        nav-selector=".ant-tabs-nav"
        @sticky-change="handleStickyChange"
        @dimensions-update="handleDimensionsUpdate"
      >
        <template #filter="{ isSticky }">
          <FilterPanel
            ref="filterPanelRef"
            :auto-apply="false"
            :class="isSticky ? 'sticky-mode' : ''"
            @filter-apply="handleFilterApply"
            @filter-change="handleFilterChange"
            @filter-clear="handleFilterClear"
          />
        </template>
      </StickyFilterWrapper>

      <!-- Tab容器 -->
      <TabContainer v-model="activeTab" :tabs="tabs" :loading="loading" @tab-change="handleTabChange" />

      <!-- 浮动操作按钮 -->
      <div class="floating-actions">
        <a-float-button-group trigger="click" type="primary">
          <template #icon>
            <Icon icon="ant-design:setting-outlined" />
          </template>

          <a-float-button tooltip="刷新所有数据" @click="handleRefreshAll">
            <template #icon>
              <Icon icon="ant-design:reload-outlined" />
            </template>
          </a-float-button>

          <a-float-button tooltip="导出当前看板" @click="handleExportDashboard">
            <template #icon>
              <Icon icon="ant-design:download-outlined" />
            </template>
          </a-float-button>

          <a-float-button tooltip="看板设置" @click="handleDashboardSettings">
            <template #icon>
              <Icon icon="ant-design:control-outlined" />
            </template>
          </a-float-button>
        </a-float-button-group>
      </div>

      <!-- 设置抽屉 -->
      <a-drawer v-model:open="settingsVisible" title="看板设置" placement="right" width="400">
        <div class="settings-content">
          <a-form layout="vertical">
            <a-form-item label="显示设置">
              <a-space direction="vertical" style="width: 100%">
                <a-switch v-model:checked="isDraggingEnabled" checked-children="启用拖拽" un-checked-children="禁用拖拽" />
              </a-space>
            </a-form-item>
            <a-form-item label="主题设置">
              <a-radio-group v-model:value="theme">
                <a-radio value="light">浅色主题</a-radio>
                <a-radio value="dark">深色主题</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-form>
        </div>
      </a-drawer>
    </PageWrapper>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
  import { PageWrapper } from '/@/components/Page';
  import { Icon } from '/@/components/Icon';
  import FilterPanel from './components/FilterPanel.vue';
  import TabContainer from './components/TabContainer.vue';
  import StickyFilterWrapper from './components/StickyFilterWrapper.vue';
  // import PerformanceMonitor from './components/PerformanceMonitor.vue';
  import { useTabConfigManager } from './hooks/useTabConfigManager';

  import { provideChartActions, provideChartData, provideChartEvents, provideChartConfig } from './hooks/useChartActions';
  import { provideStatisticsActions, provideStatisticsData, provideStatisticsState } from './hooks/useStatisticsActions';
  import { provideQueryParamsContext, setGlobalQueryParamsGetter } from './hooks/useQueryParamsContext';
  import type { FilterConfig, TabConfig } from './types/statisticDashboard';
  import { message } from 'ant-design-vue';
  import { useUserStore } from '/@/store/modules/user';
  import { useUnifiedDataLoader } from './hooks/useUnifiedDataLoader';

  // 🔥 直接使用Tab配置管理器，去掉中间包装层
  const tabConfigManager = useTabConfigManager();
  const { availableTabs: tabs, activeTabId: activeTab, activeTabCharts: currentCharts } = tabConfigManager;

  // 🔥 图表数据管理已集成到统一数据加载器中

  // 本地状态
  const settingsVisible = ref(false);
  const isDraggingEnabled = ref(false);
  const theme = ref('light');

  // 筛选器面板引用
  const filterPanelRef = ref();
  const stickyFilterRef = ref();

  // 🔥 提供查询参数上下文
  const queryParamsContext = provideQueryParamsContext();

  // 获取当前筛选器参数的函数
  const getCurrentQueryParams = () => {
    return filterPanelRef.value?.getQueryParams() || {};
  };

  // 🔥 设置查询参数获取器到上下文中
  queryParamsContext.setQueryParamsGetter(getCurrentQueryParams);

  // 🔥 同时设置全局获取器，解决上下文不可用的问题
  setGlobalQueryParamsGetter(getCurrentQueryParams);

  // 🔥 优化：使用统一的上下文系统，无需单独的状态上下文

  // 1. 提供图表上下文（内置状态管理）
  const chartActions = provideChartActions();
  provideChartData(chartActions);
  provideChartEvents(chartActions.context.handleDrillDown);
  provideChartConfig();

  // 2. 提供统计数据上下文系统
  const { loading: _statisticsLoading } = provideStatisticsState();
  const statisticsActions = provideStatisticsActions();
  const statisticsDataContext = provideStatisticsData(statisticsActions);

  // 3. 从图表上下文获取loading状态（向后兼容）
  const loading = ref(false);

  const { userInfo } = useUserStore();

  console.log('userInfo', userInfo);

  // 🚀 使用新的统一数据加载器 - 策略系统自动处理
  const unifiedLoader = useUnifiedDataLoader(statisticsDataContext);

  // 监听图表状态变化 - 通过上下文系统
  watch(
    () => loading.value,
    (loading) => {
      console.log('数据加载状态:', loading ? '加载中' : '加载完成');
    }
  );

  /**
   * 处理筛选条件应用
   */
  const handleFilterApply = async (filters: FilterConfig) => {
    console.log('应用筛选条件:', filters);

    try {
      // 获取筛选器的查询参数
      const queryParams = filterPanelRef.value?.getQueryParams();
      console.log('转换后的查询参数:', queryParams);

      if (queryParams) {
        // 🚀 使用策略系统重新加载所有异步图表和统计数据
        loading.value = true;
        try {
          await Promise.all([
            unifiedLoader.loadAllAsyncCharts(),
            unifiedLoader.loadAllAsyncStatistics(), // 🔥 新增：加载统计数据
          ]);
          message.success('筛选条件已应用');
        } finally {
          loading.value = false;
        }
      }
    } catch (error) {
      console.error('应用筛选条件失败:', error);
      message.error('应用筛选条件失败');
    }
  };

  /**
   * 处理筛选条件变化
   * 🔥 升级版：筛选参数已通过上下文系统统一管理，无需手动传递
   */
  const handleFilterChange = (filters: FilterConfig) => {
    console.log('🎯 筛选条件变化:', filters);
    console.log('✅ 筛选参数已通过策略系统自动注入到所有API调用中');

    // 🔥 筛选参数现在通过上下文系统自动管理
    // - API装饰器会自动获取最新的筛选参数
    // - 所有图表数据加载都会自动应用筛选条件
    // - 无需手动传递参数到各个组件
  };

  /**
   * 处理筛选条件清除
   */
  const handleFilterClear = () => {
    console.log('清除筛选条件');
    handleRefreshAll();
  };

  /**
   * 处理Tab切换 - 只处理业务逻辑，Tab状态由上下文系统管理
   */
  const handleTabChange = (tabId: string, tab: TabConfig) => {
    console.log(`切换到Tab: ${tab.name}`);
    tabConfigManager.setActiveTab(tabId);
    // 注意：Tab状态已由TabContainer通过上下文系统设置，这里不需要重复设置
  };

  /**
   * 刷新所有数据 - 使用筛选器参数
   */
  const handleRefreshAll = async () => {
    loading.value = true;

    try {
      // 获取当前筛选器的查询参数
      const queryParams = filterPanelRef.value?.getQueryParams() || filterPanelRef.value?.getDefaultParams();
      console.log('刷新使用的查询参数:', queryParams);

      // 🚀 使用策略系统刷新所有数据
      await Promise.all([
        unifiedLoader.loadAllAsyncCharts(),
        unifiedLoader.loadAllAsyncStatistics(), // 🔥 新增：初始化时加载统计数据
      ]);

      // 清除缓存（使用统一数据加载器）
      unifiedLoader.cleanup();

      // 使用上下文系统刷新当前Tab的所有图表
      let refreshCount = 0;
      if (currentCharts.value.length > 0) {
        for (const chart of currentCharts.value) {
          chartActions.context.refreshChart(chart.id);
          refreshCount++;
        }
      }

      message.success(`所有数据已刷新 (${refreshCount}个图表)`);
    } catch (error) {
      console.error('刷新失败:', error);
      message.error('刷新失败');
    } finally {
      loading.value = false;
    }
  };

  // 注意：Tab级别的导出、全屏功能现在由TabContainer内部处理，不再需要这些函数

  /**
   * 处理看板导出
   */
  const handleExportDashboard = () => {
    message.info('正在导出整个看板...');
    // 这里实现看板导出逻辑
  };

  /**
   * 处理看板设置
   */
  const handleDashboardSettings = () => {
    settingsVisible.value = true;
  };

  /**
   * 处理吸顶状态变化
   */
  const handleStickyChange = (isSticky: boolean) => {
    console.log(`📌 筛选器吸顶状态变化: ${isSticky ? '已激活' : '已取消'}`);

    // 可以在这里添加其他逻辑，比如：
    // - 调整页面布局
    // - 更新样式类
    // - 发送分析事件等
  };

  /**
   * 处理尺寸更新
   */
  const handleDimensionsUpdate = (dimensions: { filterHeight: number; topOffset: number }) => {
    console.log('📐 筛选器尺寸更新:', dimensions);

    // 可以在这里添加其他逻辑，比如：
    // - 调整其他组件的位置
    // - 更新全局样式变量
    // - 通知其他组件尺寸变化等
  };

  // 组件挂载
  onMounted(async () => {
    // 🔥 初始化默认激活Tab（修复activeTab找不到的问题）
    tabConfigManager.initializeActiveTab();
    console.log('✅ Tab初始化完成，当前activeTab:', activeTab.value);

    // 🔥 等待筛选器组件初始化完成，然后设置默认查询参数
    await nextTick();

    // 🚀 策略系统中查询参数通过上下文自动管理，无需手动设置
    const defaultParams = filterPanelRef.value?.getDefaultParams();
    if (defaultParams) {
      console.log('✅ 策略系统将自动使用上下文中的最新参数');

      // 🚀 触发初始数据加载（图表和统计数据）
      loading.value = true;
      try {
        await Promise.all([
          unifiedLoader.loadAllAsyncCharts(),
          unifiedLoader.loadAllAsyncStatistics(), // 🔥 新增：初始化时加载统计数据
        ]);
        console.log('✅ 初始数据加载完成（图表 + 统计数据）');
      } catch (error) {
        console.error('初始数据加载失败:', error);
        message.error('初始数据加载失败');
      } finally {
        loading.value = false;
      }
    } else {
      console.warn('无法获取筛选器默认参数');
    }
  });

  // 组件卸载
  onUnmounted(() => {});
</script>

<style lang="less" scoped>
  .statistics-dashboard {
    min-height: 100vh;
    background: #f5f5f5;

    .chart-item {
      height: 100%;
    }

    .floating-actions {
      position: fixed;
      bottom: 24px;
      right: 24px;
      z-index: 100;
    }

    .settings-content {
      .ant-form-item {
        margin-bottom: 24px;
      }
    }
  }

  // 吸顶功能相关样式
  .sticky-mode {
    border-radius: 0 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    margin-bottom: 0;
    :deep(.ant-form) {
      margin-bottom: 0;
    }

    :deep(.filter-actions) {
      margin-top: 8px;
    }
  }

  // 响应式适配
  @media (max-width: 768px) {
    .statistics-dashboard {
      .sticky-mode {
        :deep(.ant-form-item) {
          margin-bottom: 12px;
        }

        :deep(.ant-form-item-control) {
          max-width: 100%;
        }
      }
    }
  }
</style>
