/**
 * 图表上下文管理 Hook
 * 使用 Provide/Inject 模式避免深层事件传递和数据传递
 */

import { inject, provide, reactive, ref, type InjectionKey } from 'vue';
import type {
  ChartActionContext,
  ChartDataContext,
  ChartEventContext,
  ChartStateContext,
  ChartConfigContext,
  ChartDataItem,
  ChartConfig,
  ChartEventParams,
  TabConfig,
} from '../types/statisticDashboard';
import { message } from 'ant-design-vue';
import { useTabConfigManager } from './useTabConfigManager';
import { useDataSourceManager } from './useDataSourceManager';
import { getCurrentQueryParams } from './useQueryParamsContext';

// 注入键
export const CHART_ACTION_CONTEXT_KEY: InjectionKey<ChartActionContext> = Symbol('chartActionContext');
export const CHART_DATA_CONTEXT_KEY: InjectionKey<ChartDataContext> = Symbol('chartDataContext');
export const CHART_EVENT_CONTEXT_KEY: InjectionKey<ChartEventContext> = Symbol('chartEventContext');
export const CHART_STATE_CONTEXT_KEY: InjectionKey<ChartStateContext> = Symbol('chartStateContext');
export const CHART_CONFIG_CONTEXT_KEY: InjectionKey<ChartConfigContext> = Symbol('chartConfigContext');

/**
 * 提供图表操作上下文（在顶层组件中使用）
 */
export function provideChartActions() {
  // 使用新的配置管理器
  const tabConfigManager = useTabConfigManager();
  const dataSourceManager = useDataSourceManager();

  // 🔥 整合数据管理到这里，避免inject问题
  const drillDownDataStore = reactive<Record<string, ChartDataItem[]>>({});

  // 🔥 通用查询参数获取函数 - 使用全局获取器
  const getQueryParams = () => {
    return getCurrentQueryParams();
  };

  // 🔥 优化：内置状态管理，不再依赖外部状态上下文
  const chartLoadingStates = reactive<Record<string, boolean>>({});
  const chartErrorStates = reactive<Record<string, string | null>>({});

  /**
   * 🔥 优化：设置图表loading状态的辅助函数（使用内置状态管理）
   */
  const setChartLoadingState = (chartId: string, loading: boolean) => {
    console.log(`🔥 setChartLoadingState: ${chartId} loading=${loading}`);

    // 1. 设置内置状态
    chartLoadingStates[chartId] = loading;

    // 2. 通过配置更新设置loading状态（保持向后兼容）
    const currentConfig = tabConfigManager.getChartConfig(chartId);
    if (currentConfig) {
      tabConfigManager.updateChartConfig(chartId, {
        customProps: {
          ...currentConfig.customProps,
          loading,
        },
      });
      console.log(`✅ 图表loading状态已设置: ${chartId} = ${loading}`);
    } else {
      console.warn(`❌ 配置loading状态设置失败: 未找到图表配置 ${chartId}`);
    }
  };

  /**
   * 🔥 新增：设置图表错误状态
   */
  const setChartErrorState = (chartId: string, error: string | null) => {
    chartErrorStates[chartId] = error;
    console.log(`🔥 图表错误状态已设置: ${chartId} = ${error}`);
  };

  // 内部数据设置函数
  const setInternalChartData = (dataSource: string, data: ChartDataItem[]) => {
    console.log(`setInternalChartData: 设置下探数据源 - ${dataSource}, 数据长度: ${data?.length || 0}`);
    drillDownDataStore[dataSource] = data;
    console.log(`setInternalChartData: 设置完成，当前存储的数据源: ${Object.keys(drillDownDataStore).join(', ')}`);
  };

  // 更新图表配置 - 直接使用配置管理器
  const updateChartConfig = (chartId: string, newConfig: any) => {
    tabConfigManager.updateChartConfig(chartId, newConfig);
  };

  // 获取图表配置 - 直接使用配置管理器
  const getChartConfig = (chartId: string) => {
    return tabConfigManager.getChartConfig(chartId);
  };

  /**
   * 切换图表数据源
   */
  const switchChartDataSource = async (chartId: string, newDataSource: string, newTitle: string) => {
    try {
      // 🔥 设置loading状态
      setChartLoadingState(chartId, true);

      if (chartId === 'sourceOfClues') {
        // 🔥 使用统一数据加载器（策略系统）
        const { useUnifiedDataLoader } = await import('./useUnifiedDataLoader');
        const unifiedLoader = useUnifiedDataLoader();

        // 🔥 获取当前筛选器的查询参数（策略系统会自动注入）
        const queryParams = getQueryParams();

        // 使用统一数据加载器切换数据源并重新加载数据
        await unifiedLoader.switchDataSourceAndReload(chartId, newDataSource, queryParams);

        // 不需要再次显示成功消息，因为 switchDataSourceAndReload 内部已经处理了
        return true;
      } else if (chartId === 'utmChart') {
        // 处理UTM图表的数据源切换
        const { generateUtmChartConfig } = await import('../mock/clueSourceMock');

        // 生成新的UTM图表配置
        const newConfig = generateUtmChartConfig(newDataSource as 'utmSource' | 'utmMedium');

        // 更新配置
        updateChartConfig(chartId, newConfig);

        // 显示成功消息
        message.success(`已切换到 ${newTitle}`);

        return newConfig;
      } else {
        // 其他图表的处理逻辑可以在这里添加
        console.warn(`未知的图表类型: ${chartId}`);
        message.warning(`图表 ${chartId} 暂不支持数据源切换`);
      }

      // 🔥 清除loading状态
      setChartLoadingState(chartId, false);
    } catch (error) {
      // 🔥 出错时也要清除loading状态
      setChartLoadingState(chartId, false);
      console.error('切换图表数据源失败:', error);
      message.error('切换失败，请重试');
    }
  };

  /**
   * 刷新图表
   */
  const refreshChart = (chartId: string) => {
    console.log(`刷新图表: ${chartId}`);
    message.success('图表已刷新');
  };

  /**
   * 导出图表
   */
  const exportChart = (chartId: string, format: string) => {
    console.log(`导出图表: ${chartId}, 格式: ${format}`);
    message.info(`正在导出图表为 ${format.toUpperCase()}...`);
  };

  /**
   * 全屏显示图表
   */
  const fullscreenChart = (chartId: string) => {
    console.log(`全屏显示图表: ${chartId}`);
    message.info('全屏功能开发中...');
  };

  /**
   * 通用下探处理函数
   */
  const handleDrillDown = async (data: ChartDataItem, chartConfig: any) => {
    try {
      // 🔥 设置loading状态
      setChartLoadingState(chartConfig.id, true);

      const drillConfig = chartConfig.drillDown;

      if (!drillConfig || !drillConfig.enabled) {
        setChartLoadingState(chartConfig.id, false);
        message.warning('该图表不支持下探功能');
        return;
      }

      const targetLevel = drillConfig.currentLevel + 1;

      if (targetLevel > drillConfig.maxLevel) {
        setChartLoadingState(chartConfig.id, false);
        message.warning('已达到最大下探层级');
        return;
      }

      let drillDownData: ChartDataItem[];

      // 🔥 新策略模式：直接使用策略系统获取下探数据
      if (drillConfig.dataStrategy === 'async') {
        try {
          console.log('🎯 使用新策略系统获取下探数据');

          // 动态导入策略工厂（这会触发策略注册）
          const { ChartDrillDownStrategyFactory } = await import('../strategies/ChartDrillDownStrategy');

          // 🔥 强制确认策略已注册
          const allStrategies = ChartDrillDownStrategyFactory.getAllStrategies();
          console.log(`🔍 当前已注册的下探策略: [${allStrategies.map((s) => s.strategyType).join(', ')}]`);

          // 获取合适的策略
          const strategy = ChartDrillDownStrategyFactory.getStrategy(chartConfig);

          if (!strategy) {
            console.warn(`⚠️ 未找到合适的下探策略: ${chartConfig.id}`);
            drillDownData = [];
          } else {
            console.log(`📝 使用下探策略: ${strategy.strategyType} for ${chartConfig.id}`);

            // 执行策略下探
            const result = await strategy.fetchDrillDownData(data, targetLevel, chartConfig);
            drillDownData = result.data;

            console.log(`✅ 策略系统下探成功，数据条数: ${drillDownData.length}`);
          }
        } catch (error) {
          console.error('策略系统下探失败:', error);
          drillDownData = [];
        }
      } else if (drillConfig.dataStrategy === 'hybrid') {
        // 混合模式：优先策略系统，回退到静态
        try {
          const { ChartDrillDownStrategyFactory } = await import('../strategies/ChartDrillDownStrategy');
          const strategy = ChartDrillDownStrategyFactory.getStrategy(chartConfig);

          if (strategy) {
            const result = await strategy.fetchDrillDownData(data, targetLevel, chartConfig);
            drillDownData = result.data;
          } else {
            drillDownData = data.children || [];
          }
        } catch (error) {
          console.warn('策略系统获取失败，回退到静态数据:', error);
          drillDownData = data.children || [];
        }
      } else {
        // 静态数据
        drillDownData = data.children || [];
      }

      if (!drillDownData || drillDownData.length === 0) {
        setChartLoadingState(chartConfig.id, false);
        message.warning(`${data.name} 暂无详细数据`);
        return;
      }

      // 更新图表配置
      await updateChartForDrillDown(chartConfig, drillDownData, data, targetLevel);

      // 🔥 清除loading状态
      setChartLoadingState(chartConfig.id, false);
    } catch (error) {
      // 🔥 出错时也要清除loading状态
      setChartLoadingState(chartConfig.id, false);
      console.error('下探处理失败:', error);
      message.error('下探失败，请重试');
      throw error;
    }
  };

  /**
   * 通用图表配置更新函数 - 🔥 修复：不再使用inject
   */
  const updateChartForDrillDown = async (chartConfig: any, drillDownData: ChartDataItem[], parentData: ChartDataItem, targetLevel: number) => {
    try {
      const drillConfig = chartConfig.drillDown;

      // 创建下探数据源标识
      const drillDownDataSource = `${chartConfig.dataSource}_drill_${targetLevel}_${parentData.channelKey || parentData.name}`;

      console.log(
        `updateChartForDrillDown: 开始更新 - chartId: ${chartConfig.id}, 数据源: ${drillDownDataSource}, 数据长度: ${drillDownData.length}`
      );

      // 🔥 关键修复：直接使用内部数据存储，不再使用inject
      setInternalChartData(drillDownDataSource, drillDownData);
      console.log(`updateChartForDrillDown: 数据设置完成`);

      // 🔥 新策略模式：使用策略系统处理图表配置更新
      try {
        console.log(`🎯 使用策略系统更新图表配置: ${chartConfig.id}`);

        // 动态导入策略工厂
        const { ChartDrillDownStrategyFactory } = await import('../strategies/ChartDrillDownStrategy');

        // 获取合适的策略
        const strategy = ChartDrillDownStrategyFactory.getStrategy(chartConfig);

        if (strategy) {
          console.log(`📝 使用策略 ${strategy.strategyType} 更新配置`);

          // 使用策略更新配置 - 支持同步和异步
          const configUpdateResult = strategy.updateChartConfig(drillDownData, targetLevel, chartConfig, parentData);

          // 处理可能的Promise返回
          const handleConfigUpdate = (configUpdate: Partial<ChartConfig>) => {
            console.log('🎯 策略配置更新结果:', configUpdate);

            // 更新图表配置
            updateChartConfig(chartConfig.id, {
              ...chartConfig,
              ...configUpdate,
              dataSource: drillDownDataSource, // 🔥 确保使用下探数据源
              drillDown: {
                ...drillConfig,
                currentLevel: targetLevel,
              },
              customProps: {
                ...chartConfig.customProps,
                ...(configUpdate.customProps || {}),
                originalDataSource: chartConfig.dataSource, // 保存原始数据源
                drillDownData,
                parentData,
                isDrillDown: true,
                currentLevel: targetLevel,
                drillDownLevel: targetLevel, // 保持兼容性
              },
            });

            console.log(`✅ 策略系统配置更新完成: ${chartConfig.id}`);
          };

          // 检查是否是Promise并正确处理
          if (configUpdateResult instanceof Promise) {
            // 异步处理
            configUpdateResult.then(handleConfigUpdate).catch((error) => {
              console.error('异步配置更新失败:', error);
              // 使用默认配置
              handleConfigUpdate({});
            });
          } else {
            // 同步处理
            handleConfigUpdate(configUpdateResult);
          }
          return;
        } else {
          console.warn(`⚠️ 未找到配置更新策略，使用默认处理: ${chartConfig.id}`);
        }
      } catch (error) {
        console.error('策略系统配置更新失败:', error);
        console.log('回退到默认配置更新逻辑');
      }

      // 其他类型图表的处理...
      if (drillConfig.dataProvider?.updateChartConfig) {
        const configUpdate = drillConfig.dataProvider.updateChartConfig(drillDownData, targetLevel, chartConfig, parentData);

        updateChartConfig(chartConfig.id, {
          ...chartConfig,
          ...configUpdate,
          dataSource: drillDownDataSource,
          drillDown: {
            ...drillConfig,
            currentLevel: targetLevel,
          },
          customProps: {
            ...chartConfig.customProps,
            originalDataSource: chartConfig.dataSource,
            drillDownData,
            parentData,
            isDrillDown: true,
            currentLevel: targetLevel,
          },
        });
      }
    } catch (error) {
      console.error('更新图表配置失败:', error);
      throw error;
    }
  };

  /**
   * 重置图表到顶层
   */
  const resetChartToTopLevel = async (chartConfig: any) => {
    try {
      // 🔥 设置loading状态
      setChartLoadingState(chartConfig.id, true);

      const drillConfig = chartConfig.drillDown;

      if (!drillConfig || drillConfig.currentLevel === 0) {
        setChartLoadingState(chartConfig.id, false);
        return; // 已经在顶层了
      }

      console.log(`重置图表到顶层: ${chartConfig.id}, 当前层级: ${drillConfig.currentLevel}`);

      // 🔥 新策略模式：使用策略系统处理重置到顶层
      try {
        console.log(`🎯 使用策略系统重置图表到顶层: ${chartConfig.id}`);

        // 动态导入策略工厂
        const { ChartDrillDownStrategyFactory } = await import('../strategies/ChartDrillDownStrategy');

        // 获取合适的策略
        const strategy = ChartDrillDownStrategyFactory.getStrategy(chartConfig);

        if (strategy) {
          console.log(`📝 使用策略 ${strategy.strategyType} 重置配置`);

          // 使用策略重置到顶层配置 - 处理可能的Promise
          const topLevelConfigResult = strategy.resetToTopLevelConfig(chartConfig);
          let topLevelConfig: Partial<ChartConfig>;

          if (topLevelConfigResult instanceof Promise) {
            topLevelConfig = await topLevelConfigResult;
          } else {
            topLevelConfig = topLevelConfigResult;
          }

          console.log('🎯 策略重置配置结果:', topLevelConfig);

          // 恢复数据源信息
          const originalDataSource = chartConfig.customProps?.originalDataSource || chartConfig.dataSource;
          const currentDataSourceType = chartConfig.customProps?.currentDataSource || originalDataSource;

          // 更新图表配置回到顶层
          updateChartConfig(chartConfig.id, {
            ...chartConfig,
            ...topLevelConfig,
            dataSource: currentDataSourceType, // 🔥 使用当前数据源类型
            drillDown: {
              ...chartConfig.drillDown,
              currentLevel: 0,
            },
            customProps: {
              ...chartConfig.customProps,
              ...topLevelConfig.customProps,
              originalDataSource: undefined, // 清除原始数据源记录
              drillDownData: undefined,
              parentData: undefined,
              isDrillDown: false,
              currentLevel: 0,
              drillDownLevel: 0, // 重置下探层级
              loading: true, // 设置加载状态
              needsAsyncData: true, // 标记需要异步数据
            },
          });

          console.log(`✅ 策略系统重置配置完成: ${chartConfig.id}`);

          // 重新加载数据
          try {
            // 🔥 使用统一数据加载器（策略系统）
            const { useUnifiedDataLoader } = await import('./useUnifiedDataLoader');
            const unifiedLoader = useUnifiedDataLoader();
            const queryParams = getQueryParams();

            console.log(`🔄 重新加载顶层数据 - 数据源: ${currentDataSourceType}`);
            await unifiedLoader.refreshChartData(chartConfig.id, queryParams);
            console.log(`✅ 顶层数据加载完成: ${chartConfig.id}`);
          } catch (dataError) {
            setChartLoadingState(chartConfig.id, false);
            console.error('重置到顶层时数据加载失败:', dataError);
            message.error('返回顶层时数据加载失败');
            return;
          }
        } else {
          console.warn(`⚠️ 未找到重置策略，使用默认处理: ${chartConfig.id}`);

          // 默认重置逻辑
          const originalDataSource = chartConfig.customProps?.originalDataSource || chartConfig.dataSource;
          updateChartConfig(chartConfig.id, {
            ...chartConfig,
            dataSource: originalDataSource,
            drillDown: {
              ...chartConfig.drillDown,
              currentLevel: 0,
            },
            customProps: {
              ...chartConfig.customProps,
              originalDataSource: undefined,
              drillDownData: undefined,
              parentData: undefined,
              isDrillDown: false,
              currentLevel: 0,
              drillDownLevel: 0,
              loading: false,
            },
          });
        }
      } catch (error) {
        console.error('策略系统重置失败:', error);
        console.log('回退到默认重置逻辑');

        // 回退逻辑
        const originalDataSource = chartConfig.customProps?.originalDataSource || chartConfig.dataSource;
        updateChartConfig(chartConfig.id, {
          ...chartConfig,
          dataSource: originalDataSource,
          drillDown: {
            ...chartConfig.drillDown,
            currentLevel: 0,
          },
          customProps: {
            ...chartConfig.customProps,
            originalDataSource: undefined,
            drillDownData: undefined,
            parentData: undefined,
            isDrillDown: false,
            currentLevel: 0,
            drillDownLevel: 0,
            loading: false,
          },
        });
      }

      // 🔥 清除loading状态
      setChartLoadingState(chartConfig.id, false);
      message.success('已返回顶层');
    } catch (error) {
      // 🔥 出错时也要清除loading状态
      setChartLoadingState(chartConfig.id, false);
      console.error('重置到顶层失败:', error);
      message.error('返回顶层失败');
      throw error;
    }
  };

  // 🔥 优化：创建包含状态管理的统一上下文对象
  const context: ChartActionContext = {
    switchChartDataSource,
    refreshChart,
    exportChart,
    fullscreenChart,
    // 添加通用下探处理函数
    handleDrillDown,
    // 添加重置到顶层函数
    resetChartToTopLevel,

    // 🔥 新增：内置状态管理
    chartLoadingStates,
    chartErrorStates,
    setChartLoading: setChartLoadingState,
    setChartError: setChartErrorState,
  };

  // 提供上下文
  provide(CHART_ACTION_CONTEXT_KEY, context);

  return {
    context,
    updateChartConfig,
    getChartConfig,
    // 🔥 新增：暴露内部数据存储给数据上下文使用
    drillDownDataStore,
    setInternalChartData,
    // 暴露配置管理器
    tabConfigManager,
    dataSourceManager,
  };
}

/**
 * 提供图表数据上下文 - 🔥 修复：集成到chartActions的数据存储
 */
export function provideChartData(chartActionsReturn?: ReturnType<typeof provideChartActions>) {
  /**
   * 获取图表数据 - 使用新的数据源管理器
   */
  const getChartData = (dataSource: string): ChartDataItem[] => {
    console.log(`getChartData: 请求数据源 - ${dataSource}`);

    // 🔥 优先检查chartActions的下探数据存储
    if (chartActionsReturn?.drillDownDataStore[dataSource]) {
      console.log(`getChartData: 从下探数据存储获取 - 长度: ${chartActionsReturn.drillDownDataStore[dataSource].length}`);
      return chartActionsReturn.drillDownDataStore[dataSource];
    }

    // 使用数据源管理器获取数据
    if (chartActionsReturn?.dataSourceManager) {
      const result = chartActionsReturn.dataSourceManager.getDataSource(dataSource);
      console.log(`getChartData: 从数据源管理器获取数据 - 长度: ${result?.length || 0}`);
      return result || [];
    }

    // 兜底：返回空数组
    console.warn(`getChartData: 未找到数据源 - ${dataSource}`);
    return [];
  };

  /**
   * 刷新图表数据 - 简化逻辑
   */
  const refreshChartData = async (chartId: string): Promise<void> => {
    // 清除相关的下探数据
    if (chartActionsReturn?.drillDownDataStore) {
      Object.keys(chartActionsReturn.drillDownDataStore).forEach((key) => {
        if (key.includes(chartId)) {
          delete chartActionsReturn.drillDownDataStore[key];
        }
      });
    }
    console.log(`刷新图表数据: ${chartId}`);
  };

  /**
   * 设置图表数据（主要用于下探场景）- 🔥 修复：使用chartActions的数据存储
   */
  const setChartData = (dataSource: string, data: ChartDataItem[]) => {
    console.log(`setChartData: 设置下探数据源 - ${dataSource}, 数据长度: ${data?.length || 0}`);
    if (chartActionsReturn?.setInternalChartData) {
      chartActionsReturn.setInternalChartData(dataSource, data);
    } else {
      console.warn('setChartData: chartActions不可用，无法设置数据');
    }
  };

  /**
   * 清除数据缓存 - 简化为清除下探数据
   */
  const clearDataCache = () => {
    if (chartActionsReturn?.drillDownDataStore) {
      Object.keys(chartActionsReturn.drillDownDataStore).forEach((key) => {
        delete chartActionsReturn.drillDownDataStore[key];
      });
      console.log('clearDataCache: 已清除所有下探数据');
    }
  };

  const dataContext: ChartDataContext = {
    getChartData,
    setChartData,
    refreshChartData,
    clearDataCache,
  };

  provide(CHART_DATA_CONTEXT_KEY, dataContext);

  return dataContext;
}

/**
 * 提供图表事件上下文
 */
export function provideChartEvents(handleDrillDownFn?: (data: ChartDataItem, chartConfig: any) => Promise<void>) {
  /**
   * 图表点击事件
   */
  const onChartClick = (params: ChartEventParams) => {
    console.log('图表点击:', params);
  };

  /**
   * 数据下探事件 - 通用下探处理
   */
  const onDrillDown = async (data: ChartDataItem, level: number, chartConfig?: any) => {
    console.log('数据下探:', data.name, level);

    try {
      // 使用传入的下探处理函数
      if (handleDrillDownFn) {
        await handleDrillDownFn(data, chartConfig);
      } else {
        message.warning('下探功能暂不可用');
      }
    } catch (error) {
      console.error('下探处理失败:', error);
      message.error('下探失败，请重试');
    }
  };

  /**
   * 数据点击事件
   */
  const onDataClick = (data: ChartDataItem) => {
    console.log('数据点击:', data.name);
  };

  /**
   * 图表双击事件
   */
  const onChartDblClick = (params: ChartEventParams) => {
    console.log('图表双击:', params);
  };

  /**
   * 图表区域点击事件
   */
  const onChartAreaClick = (event: any) => {
    console.log('图表区域点击:', event);
  };

  const eventContext: ChartEventContext = {
    onChartClick,
    onDrillDown,
    onDataClick,
    onChartDblClick,
    onChartAreaClick,
  };

  provide(CHART_EVENT_CONTEXT_KEY, eventContext);

  return eventContext;
}

/**
 * 提供图表状态上下文
 */
export function provideChartState(initialLoading = false) {
  const loading = ref(initialLoading);
  const chartLoadingStates = reactive<Record<string, boolean>>({});

  /**
   * 设置图表loading状态
   */
  const setChartLoading = (chartId: string, isLoading: boolean) => {
    chartLoadingStates[chartId] = isLoading;
  };

  const stateContext: ChartStateContext = {
    loading: loading.value,
    chartLoadingStates,
    setChartLoading,
  };

  provide(CHART_STATE_CONTEXT_KEY, stateContext);

  return {
    stateContext,
    loading,
    chartLoadingStates,
    setChartLoading,
  };
}

/**
 * 提供图表配置上下文
 */
export function provideChartConfig() {
  // 使用新的配置管理器
  const tabConfigManager = useTabConfigManager();
  const currentTabId = ref<string>('');

  /**
   * 获取图表配置
   */
  const getChartConfig = (chartId: string): ChartConfig | undefined => {
    return tabConfigManager.getChartConfig(chartId);
  };

  /**
   * 获取所有Tab配置
   */
  const getAllTabs = (): TabConfig[] => {
    return tabConfigManager.getAllTabs();
  };

  /**
   * 获取当前激活的Tab
   */
  const getCurrentTab = (): TabConfig | undefined => {
    if (!currentTabId.value) return undefined;
    return tabConfigManager.getTabConfig(currentTabId.value);
  };

  /**
   * 设置当前激活的Tab
   */
  const setCurrentTab = (tabId: string) => {
    currentTabId.value = tabId;
  };

  /**
   * 获取图表下探状态
   */
  const getChartDrillDownState = (chartId: string) => {
    const config = getChartConfig(chartId);
    if (!config?.drillDown) return null;

    const currentLevel = config.drillDown.currentLevel || 0;
    const isInDrillDown = currentLevel > 0;

    return { currentLevel, isInDrillDown };
  };

  const configContext: ChartConfigContext = {
    getChartConfig,
    getAllTabs,
    getCurrentTab,
    setCurrentTab,
    getChartDrillDownState,
  };

  provide(CHART_CONFIG_CONTEXT_KEY, configContext);

  return {
    configContext,
    currentTabId,
  };
}

/**
 * 使用图表操作上下文（在子组件中使用）
 */
export function useChartActions() {
  const context = inject(CHART_ACTION_CONTEXT_KEY);

  if (!context) {
    throw new Error('useChartActions must be used within a component that provides ChartActionContext');
  }

  return context;
}

/**
 * 可选的图表操作Hook（提供默认实现）
 */
export function useChartActionsOptional() {
  const context = inject(CHART_ACTION_CONTEXT_KEY, null);

  // 如果没有提供上下文，返回默认实现
  if (!context) {
    return {
      switchChartDataSource: (_chartId: string, _newDataSource: string, newTitle: string) => {
        console.warn('ChartActionContext not provided, using default implementation');
        message.info(`切换到 ${newTitle} (默认实现)`);
      },
      refreshChart: (_chartId: string) => {
        console.warn('ChartActionContext not provided, using default implementation');
        message.info('刷新图表 (默认实现)');
      },
      exportChart: (_chartId: string, _format: string) => {
        console.warn('ChartActionContext not provided, using default implementation');
        message.info(`导出图表 (默认实现)`);
      },
      fullscreenChart: (_chartId: string) => {
        console.warn('ChartActionContext not provided, using default implementation');
        message.info('全屏显示 (默认实现)');
      },
      handleDrillDown: async (_data: ChartDataItem, _chartConfig: any) => {
        console.warn('ChartActionContext not provided, using default implementation');
        message.info('下探功能 (默认实现)');
      },
      resetChartToTopLevel: async (_chartConfig: any) => {
        console.warn('ChartActionContext not provided, using default implementation');
        message.info('返回顶层 (默认实现)');
      },
    };
  }

  return context;
}

/**
 * 使用图表数据上下文
 */
export function useChartData() {
  const context = inject(CHART_DATA_CONTEXT_KEY);

  if (!context) {
    throw new Error('useChartData must be used within a component that provides ChartDataContext');
  }

  return context;
}

/**
 * 可选的图表数据Hook
 */
export function useChartDataOptional() {
  const context = inject(CHART_DATA_CONTEXT_KEY, null);

  if (!context) {
    return {
      getChartData: (_dataSource: string) => [],
      setChartData: (_dataSource: string, _data: ChartDataItem[]) => {},
      refreshChartData: async (_chartId: string) => {},
      clearDataCache: () => {},
    };
  }

  return context;
}

/**
 * 使用图表事件上下文
 */
export function useChartEvents() {
  const context = inject(CHART_EVENT_CONTEXT_KEY);

  if (!context) {
    throw new Error('useChartEvents must be used within a component that provides ChartEventContext');
  }

  return context;
}

/**
 * 可选的图表事件Hook
 */
export function useChartEventsOptional() {
  const context = inject(CHART_EVENT_CONTEXT_KEY, null);

  if (!context) {
    return {
      onChartClick: (_params: ChartEventParams) => {},
      onDrillDown: async (_data: ChartDataItem, _level: number, _chartConfig?: any) => {},
      onDataClick: (_data: ChartDataItem) => {},
      onChartDblClick: (_params: ChartEventParams) => {},
      onChartAreaClick: (_event: any) => {},
    };
  }

  return context;
}

/**
 * 使用图表状态上下文
 */
export function useChartState() {
  const context = inject(CHART_STATE_CONTEXT_KEY);

  if (!context) {
    throw new Error('useChartState must be used within a component that provides ChartStateContext');
  }

  return context;
}

/**
 * 可选的图表状态Hook
 */
export function useChartStateOptional() {
  const context = inject(CHART_STATE_CONTEXT_KEY, null);

  if (!context) {
    return {
      loading: false,
      chartLoadingStates: {},
      setChartLoading: (_chartId: string, _loading: boolean) => {},
      chartUpdateTimes: {},
      setChartUpdateTime: (_chartId: string, _time: Date) => {},
    };
  }

  return context;
}

/**
 * 使用图表配置上下文
 */
export function useChartConfig() {
  const context = inject(CHART_CONFIG_CONTEXT_KEY);

  if (!context) {
    throw new Error('useChartConfig must be used within a component that provides ChartConfigContext');
  }

  return context;
}

/**
 * 可选的图表配置Hook
 */
export function useChartConfigOptional() {
  const context = inject(CHART_CONFIG_CONTEXT_KEY, null);

  if (!context) {
    return {
      getChartConfig: (_chartId: string) => undefined,
      getAllTabs: () => [],
      getCurrentTab: () => undefined,
      setCurrentTab: (_tabId: string) => {},
      getChartDrillDownState: (_chartId: string) => null,
    };
  }

  return context;
}
