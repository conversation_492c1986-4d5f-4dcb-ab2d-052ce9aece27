/**
 * Tab配置管理Hook
 * 统一管理Tab配置，替代mock/data.ts中的配置管理
 */

import { reactive, computed, readonly, ref } from 'vue';
import type { TabConfig, ChartConfig, StatisticsConfig, StatisticsItem } from '../types/statisticDashboard';
import { AuthEnum } from '../../../enums/authEnum';
import { useI18n } from '../../../hooks/web/useI18n';
import { usePermission } from '../../../hooks/web/usePermission';

// 导入具体的图表配置
import { clueEffectivenessChartConfigs, utmChartConfigs } from '../mock/clueSourceMock';
import { followUpOnCluesChartConfigs } from '../mock/followUpOnCluesMock';
import { sourceOfCluesChartConfigs } from '../config/clueSourceChartConfig';

const { t } = useI18n('common');

/**
 * Tab配置管理Hook
 */
export function useTabConfigManager() {
  const { hasPermission } = usePermission();

  // Tab配置存储
  const tabConfigs = reactive<Record<string, TabConfig>>({});

  // 图表配置存储
  const chartConfigs = reactive<Record<string, ChartConfig>>({});

  // Tab状态管理
  const activeTabId = ref<string>('');

  /**
   * 初始化默认Tab配置
   */
  const initializeDefaultTabs = () => {
    const defaultTabs: TabConfig[] = [
      {
        id: 'clueStatistics',
        name: t('clueStatistics'),
        layout: 'grid',
        groups: [
          {
            title: t('overviewOfClues'),
            statisticsConfig: {
              id: 'clueOverview',
              dataSource: 'clueOverview',
              needsAsyncData: true,
              loadingStrategy: 'clue-overview',
              items: [
                {
                  id: 'clueAllCount',
                  title: t('totalNumberOfClues'),
                  total: 0,
                  increase: 0,
                  decrease: undefined, // 🔥 初始状态：同比数据未知
                  core: true,
                  loading: true,
                  needsAsyncData: true,
                  dataSource: 'clueOverview',
                },
                {
                  id: 'clueValidCount',
                  title: t('totalNumberOfEffectiveClues'),
                  total: 0,
                  increase: 0,
                  decrease: undefined, // 🔥 初始状态：同比数据未知
                  core: false,
                  loading: true,
                  needsAsyncData: true,
                  dataSource: 'clueOverview',
                },
                {
                  id: 'clueDealCount',
                  title: t('clueTradingVolume'),
                  total: 0,
                  increase: 0,
                  decrease: undefined, // 🔥 初始状态：同比数据未知
                  core: false,
                  loading: true,
                  needsAsyncData: true,
                  dataSource: 'clueOverview',
                },
                {
                  id: 'clueFollowPercent',
                  title: t('leadFollowUpRate'),
                  total: 0,
                  increase: 0,
                  decrease: undefined, // 🔥 初始状态：同比数据未知
                  core: false,
                  loading: true,
                  needsAsyncData: true,
                  dataSource: 'clueOverview',
                },
                {
                  id: 'clueValidPercent',
                  title: t('cluesAreEfficient'),
                  total: 0,
                  increase: 0,
                  decrease: undefined, // 🔥 初始状态：同比数据未知
                  core: false,
                  loading: true,
                  needsAsyncData: true,
                  dataSource: 'clueOverview',
                },
                {
                  id: 'clueWinPercent',
                  title: t('clueWinRate'),
                  total: 0,
                  increase: 0,
                  decrease: undefined, // 🔥 初始状态：同比数据未知
                  core: false,
                  loading: true,
                  needsAsyncData: true,
                  dataSource: 'clueOverview',
                },
              ],
            },
            chartList: [],
          },
          {
            // 线索来源
            title: t('sourceOfClues'),
            chartList: [...sourceOfCluesChartConfigs, ...utmChartConfigs, ...clueEffectivenessChartConfigs],
          },
          {
            title: '线索跟进',
            chartList: [...followUpOnCluesChartConfigs],
          },
        ],
        auth: AuthEnum.View_Clue_Statistics,
      },
      {
        id: 'workOrderStatistics',
        name: t('workOrderStatistics'),
        layout: 'grid',
        groups: [],
        auth: AuthEnum.View_Work_Order_Statistics,
      },
      {
        id: 'vehicleStatistics',
        name: t('vehicleStatistics'),
        layout: 'grid',
        groups: [],
        auth: AuthEnum.View_Vehicle_Statistics,
      },
      {
        id: 'userStatistics',
        name: t('userStatistics'),
        layout: 'grid',
        groups: [],
        auth: AuthEnum.View_User_Statistics,
      },
    ];

    // 存储Tab配置
    defaultTabs.forEach((tab) => {
      tabConfigs[tab.id] = tab;

      // 同时存储图表配置
      tab.groups?.forEach((group) => {
        group.chartList?.forEach((chart) => {
          chartConfigs[chart.id] = chart;
        });
      });
    });
  };

  /**
   * 获取所有Tab配置
   */
  const getAllTabs = (): TabConfig[] => {
    return Object.values(tabConfigs).filter((tab) => !tab.auth || hasPermission(tab.auth));
  };

  /**
   * 获取指定Tab配置
   */
  const getTabConfig = (tabId: string): TabConfig | undefined => {
    const tab = tabConfigs[tabId];
    if (!tab) return undefined;

    // 检查权限
    if (tab.auth && !hasPermission(tab.auth)) {
      return undefined;
    }

    return tab;
  };

  /**
   * 获取图表配置
   */
  const getChartConfig = (chartId: string): ChartConfig | undefined => {
    return chartConfigs[chartId];
  };

  /**
   * 更新图表配置
   */
  const updateChartConfig = (chartId: string, updates: Partial<ChartConfig>) => {
    if (chartConfigs[chartId]) {
      Object.assign(chartConfigs[chartId], updates);
    }
  };

  /**
   * 添加新的Tab配置
   */
  const addTabConfig = (tab: TabConfig) => {
    tabConfigs[tab.id] = tab;

    // 同时添加图表配置
    tab.groups?.forEach((group) => {
      group.chartList?.forEach((chart) => {
        chartConfigs[chart.id] = chart;
      });
    });
  };

  /**
   * 获取Tab中的所有图表配置
   */
  const getTabCharts = (tabId: string): ChartConfig[] => {
    const tab = getTabConfig(tabId);
    if (!tab) return [];

    return tab.groups?.flatMap((group) => group.chartList || []) || [];
  };

  /**
   * 重置所有配置
   */
  const resetConfigs = () => {
    Object.keys(tabConfigs).forEach((key) => delete tabConfigs[key]);
    Object.keys(chartConfigs).forEach((key) => delete chartConfigs[key]);
    activeTabId.value = '';
    initializeDefaultTabs();
  };

  /**
   * 设置激活的Tab
   */
  const setActiveTab = (tabId: string) => {
    const tab = getTabConfig(tabId);
    if (tab) {
      activeTabId.value = tabId;
      return true;
    }
    return false;
  };

  /**
   * 获取当前激活的Tab
   */
  const getActiveTab = (): TabConfig | undefined => {
    return getTabConfig(activeTabId.value);
  };

  /**
   * 获取当前激活Tab的图表列表
   */
  const getActiveTabCharts = (): ChartConfig[] => {
    const tab = getActiveTab();
    return tab?.groups?.flatMap((group) => group.chartList || []) || [];
  };

  /**
   * 初始化默认激活Tab
   */
  const initializeActiveTab = () => {
    const tabs = getAllTabs();
    if (tabs.length > 0 && !activeTabId.value) {
      activeTabId.value = tabs[0].id;
    }
  };

  // 计算属性：可用的Tab列表
  const availableTabs = computed(() => getAllTabs());

  // 计算属性：当前激活的Tab
  const activeTab = computed(() => getActiveTab());

  // 计算属性：当前激活Tab的图表列表
  const activeTabCharts = computed(() => getActiveTabCharts());

  // 计算属性：配置统计
  const configStats = computed(() => ({
    totalTabs: Object.keys(tabConfigs).length,
    totalCharts: Object.keys(chartConfigs).length,
    availableTabsCount: availableTabs.value.length,
    activeTabId: activeTabId.value,
  }));

  // 初始化默认配置
  initializeDefaultTabs();

  return {
    // 状态（只读）
    tabConfigs: readonly(tabConfigs),
    chartConfigs: readonly(chartConfigs),
    availableTabs,
    activeTab,
    activeTabCharts,
    activeTabId: readonly(activeTabId),
    configStats,

    // 图表配置方法
    getAllTabs,
    getTabConfig,
    getChartConfig,
    updateChartConfig,
    addTabConfig,
    getTabCharts,
    resetConfigs,
    setActiveTab,
    getActiveTab,
    getActiveTabCharts,
    initializeActiveTab,

    // 🔥 新增：统计数据管理方法
    getStatisticsConfig,
    getAllStatisticsConfigs,
    updateStatisticsData,
    getStatisticsData,
    setStatisticsItemLoading,
    setStatisticsConfigLoading,
  };

  // ========== 统计数据管理方法 ==========

  /**
   * 获取统计配置
   */
  function getStatisticsConfig(configId: string): StatisticsConfig | null {
    for (const tab of Object.values(tabConfigs)) {
      for (const group of tab.groups) {
        if (group.statisticsConfig?.id === configId) {
          return group.statisticsConfig;
        }
      }
    }
    return null;
  }

  /**
   * 获取所有统计配置
   */
  function getAllStatisticsConfigs(): StatisticsConfig[] {
    const configs: StatisticsConfig[] = [];
    for (const tab of Object.values(tabConfigs)) {
      for (const group of tab.groups) {
        if (group.statisticsConfig) {
          configs.push(group.statisticsConfig);
        }
      }
    }
    return configs;
  }

  /**
   * 更新统计数据
   */
  function updateStatisticsData(configId: string, statisticsData: StatisticsItem[]): void {
    for (const tab of Object.values(tabConfigs)) {
      for (const group of tab.groups) {
        if (group.statisticsConfig?.id === configId) {
          // 更新统计配置中的数据
          group.statisticsConfig.items = statisticsData;
          console.log(`✅ 更新统计数据: ${configId}, 数据条数: ${statisticsData.length}`);
          return;
        }
      }
    }
    console.warn(`⚠️ 未找到统计配置: ${configId}`);
  }

  /**
   * 获取统计数据（兼容旧版本）
   */
  function getStatisticsData(configId: string): StatisticsItem[] {
    const config = getStatisticsConfig(configId);
    return config?.items || [];
  }

  /**
   * 设置统计项加载状态
   */
  function setStatisticsItemLoading(configId: string, itemId: string, loading: boolean): void {
    const config = getStatisticsConfig(configId);
    if (config) {
      const item = config.items.find((item) => item.id === itemId);
      if (item) {
        item.loading = loading;
      }
    }
  }

  /**
   * 批量设置统计配置加载状态
   */
  function setStatisticsConfigLoading(configId: string, loading: boolean): void {
    const config = getStatisticsConfig(configId);
    if (config) {
      config.items.forEach((item) => {
        item.loading = loading;
      });
    }
  }
}
