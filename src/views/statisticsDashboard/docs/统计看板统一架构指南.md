# 统计看板统一架构指南

## 📋 概述

统计看板采用现代化的Vue 3架构，通过**上下文系统 + 策略模式 + 数据源管理器**实现了完全的组件解耦和统一数据管理。

**核心特点**：
- 🎯 **零透传架构**：0层数据传递，0个不必要emit，0个组件暴露
- 🏗️ **统一上下文系统**：图表和统计数据使用相同的架构模式
- 📊 **策略模式**：自动选择合适的数据加载策略，支持扩展
- 🗄️ **统一数据源管理**：缓存、元数据、类型安全的数据管理
- 🔧 **零手动传参**：API装饰器自动注入筛选参数
- ✨ **内置状态管理**：操作上下文内置状态管理，无需单独状态上下文

## 🏗️ 整体架构

### 架构层次图

```
┌─────────────────────────────────────────────────────────────┐
│                    统计看板主组件                              │
│              UserOperationStatisticsDashboard               │
└─────────────────────┬───────────────────────────────────────┘
                      │ Provide 上下文
┌─────────────────────▼───────────────────────────────────────┐
│                   上下文系统层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ ChartActions    │  │StatisticsActions│  │QueryParams   │ │
│  │ (内置状态管理)   │  │ (内置状态管理)   │  │Context       │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │ 统一数据管理
┌─────────────────────▼───────────────────────────────────────┐
│                  数据源管理器                                 │
│              useDataSourceManager                           │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │  统一数据源映射 (支持图表和统计数据)                        │ │
│  │  - 缓存管理    - 元数据管理    - 类型安全                  │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │ 策略选择
┌─────────────────────▼───────────────────────────────────────┐
│                   策略工厂层                                  │
│  ┌─────────────────┐              ┌─────────────────────────┐ │
│  │ChartDataLoading │              │StatisticsDataLoading    │ │
│  │StrategyFactory  │              │StrategyFactory          │ │
│  └─────────────────┘              └─────────────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │ 具体策略实现
┌─────────────────────▼───────────────────────────────────────┐
│                   策略实现层                                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ClueSourceChart  │  │ClueOverview     │  │DefaultMock   │ │
│  │Strategy         │  │Statistics       │  │Strategy      │ │
│  │                 │  │Strategy         │  │              │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │ API装饰器自动注入参数
┌─────────────────────▼───────────────────────────────────────┐
│                    API调用层                                 │
│              ApiCallDecorator                               │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │  自动注入筛选参数 → API调用 → 数据转换 → 缓存存储          │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 核心系统详解

### 1. 上下文系统 (Context System)

#### 设计原则
- **内置状态管理**：操作上下文内置状态管理，避免注入顺序问题
- **统一架构**：图表和统计数据使用相同的上下文模式
- **类型安全**：完整的TypeScript类型定义

#### 图表操作上下文
```typescript
interface ChartActionContext {
  // 核心操作
  switchChartDataSource: (chartId: string, newDataSource: string, newTitle: string) => void;
  refreshChart: (chartId: string) => void;
  exportChart: (chartId: string, format: string) => void;
  fullscreenChart: (chartId: string) => void;
  
  // 下探功能
  handleDrillDown?: (data: ChartDataItem, chartConfig: any) => Promise<void>;
  resetChartToTopLevel?: (chartConfig: any) => Promise<void>;
  
  // 内置状态管理
  chartLoadingStates: Record<string, boolean>;
  chartErrorStates: Record<string, string | null>;
  setChartLoading: (chartId: string, loading: boolean) => void;
  setChartError: (chartId: string, error: string | null) => void;
}
```

#### 统计数据操作上下文
```typescript
interface StatisticsActionContext {
  // 核心操作
  refreshStatistics: (configId: string) => Promise<void>;
  reloadAllStatistics: () => Promise<void>;
  
  // 内置状态管理
  statisticsLoadingStates: Record<string, boolean>;
  statisticsErrorStates: Record<string, string | null>;
  setStatisticsLoading: (configId: string, loading: boolean) => void;
  setStatisticsError: (configId: string, error: string | null) => void;
}
```

### 2. 数据源管理器 (Data Source Manager)

#### 核心功能
- **统一存储**：图表和统计数据使用同一个数据源映射
- **类型安全**：通过泛型支持不同数据类型
- **元数据管理**：完整的数据源元信息管理
- **缓存机制**：自动缓存和更新数据

#### 使用示例
```typescript
const dataSourceManager = useDataSourceManager();

// 存储图表数据
dataSourceManager.setDataSource<ChartDataItem>('sourceOfClues', chartData, {
  name: '线索来源',
  type: 'chart',
  dataType: 'ChartDataItem'
});

// 存储统计数据
dataSourceManager.setDataSource<StatisticsItem>('clueOverview', statsData, {
  name: '线索总览',
  type: 'statistics', 
  dataType: 'StatisticsItem'
});

// 获取数据（类型安全）
const chartData = dataSourceManager.getDataSource<ChartDataItem>('sourceOfClues');
const statsData = dataSourceManager.getDataSource<StatisticsItem>('clueOverview');
```

### 3. 策略模式 (Strategy Pattern)

#### 图表数据加载策略
```typescript
// 抽象基类
export abstract class BaseChartDataLoadingStrategy {
  protected apiDecorator: IApiCallDecorator;
  protected dataSourceManager: ReturnType<typeof useDataSourceManager>;
  
  // 模板方法：标准数据加载流程
  async loadData(chartConfig: ChartConfig, customParams = {}) {
    // 1. 验证配置
    // 2. 设置加载状态  
    // 3. 通过装饰器调用API
    // 4. 数据转换
    // 5. 存储到数据源管理器
    // 6. 返回结果
  }
}

// 具体策略实现
export class ClueSourceChartStrategy extends BaseChartDataLoadingStrategy {
  readonly strategyType = 'clue-source';
  readonly supportedChartTypes = ['bar', 'column'];
  
  protected async fetchApiData(params: commonQueryParams) {
    const { queryAllClueSource } = await import('../api');
    return await queryAllClueSource(params);
  }
}
```

#### 统计数据加载策略
```typescript
export abstract class BaseStatisticsDataLoadingStrategy {
  protected apiDecorator: IStatisticsApiDecorator;
  protected dataSourceManager: ReturnType<typeof useDataSourceManager>;
  
  async loadStatisticsData(config: StatisticsConfig, params?) {
    // 1. 使用装饰器自动注入参数
    // 2. 转换数据
    // 3. 存储到数据源管理器
    // 4. 返回结果
  }
}
```

### 4. API装饰器 (API Decorator)

#### 自动参数注入
```typescript
export class ApiCallDecorator implements IApiCallDecorator {
  async decorateApiCall<T>(apiCall: (params: commonQueryParams) => Promise<T>, customParams?) {
    // 🔥 自动获取当前筛选参数
    const currentParams = getCurrentQueryParams();
    
    // 合并参数：自定义参数 > 当前筛选参数
    const finalParams = { ...currentParams, ...customParams };
    
    // 调用API
    return await apiCall(finalParams);
  }
}
```

## 🔄 数据流程

### 完整数据流
```
用户操作 → 筛选器更新 → QueryParamsContext
    ↓
API装饰器自动注入参数 → 策略工厂选择策略
    ↓  
具体策略执行 → API调用 → 数据转换
    ↓
数据源管理器存储 → 上下文系统更新 → 组件响应
```

### 关键优势
1. **零手动传参**：筛选参数自动注入，无需手动获取和传递
2. **统一架构**：图表和统计数据使用相同的模式
3. **自动缓存**：数据源管理器自动处理缓存
4. **类型安全**：完整的TypeScript支持
5. **易扩展**：新增功能只需添加策略

## 📊 架构优势

| 特性 | 传统方式 | 统一架构 | 改进 |
|------|----------|----------|------|
| 数据传递 | Props层层传递 | 上下文直接注入 | 0层传递 |
| 状态管理 | 分散在各组件 | 内置状态管理 | 统一管理 |
| 参数传递 | 手动获取传递 | 装饰器自动注入 | 零手动传参 |
| 数据缓存 | 各自管理 | 统一数据源管理器 | 统一缓存 |
| 代码复用 | 重复实现 | 策略模式 | 高度复用 |
| 类型安全 | 部分支持 | 完整TypeScript | 完全类型安全 |

这个统一架构实现了真正的**零透传、零手动传参、统一管理**，为统计看板提供了强大、灵活、易维护的技术基础。

## 🛠️ 实践指南

### 新增图表数据加载策略

1. **创建策略类**：
```typescript
export class NewChartStrategy extends BaseChartDataLoadingStrategy {
  readonly strategyType = 'new-chart';
  readonly supportedChartTypes = ['line', 'area'];

  protected async fetchApiData(params: commonQueryParams) {
    const { queryNewData } = await import('../api');
    return await queryNewData(params);
  }

  protected async transformData(apiData: any): Promise<ChartDataItem[]> {
    // 数据转换逻辑
    return transformedData;
  }
}
```

2. **注册策略**：
```typescript
ChartDataLoadingStrategyFactory.registerStrategy(new NewChartStrategy());
```

3. **配置图表**：
```typescript
const chartConfig = {
  id: 'newChart',
  type: 'line',
  customProps: {
    loadingStrategy: 'new-chart', // 指定策略
    needsAsyncData: true
  }
};
```

### 新增统计数据策略

1. **创建统计策略**：
```typescript
export class NewStatisticsStrategy extends BaseStatisticsDataLoadingStrategy {
  readonly strategyType = 'new-statistics';
  readonly supportedDataSources = ['newStats'];

  protected async fetchApiData(params: commonQueryParams) {
    const { queryNewStats } = await import('../api');
    return await queryNewStats(params);
  }

  protected async transformData(apiData: any, config: StatisticsConfig) {
    // 转换为 StatisticsItem[]
    return transformedStats;
  }
}
```

2. **注册策略**：
```typescript
StatisticsDataLoadingStrategyFactory.registerStrategy(new NewStatisticsStrategy());
```

### 组件中使用上下文

```typescript
// 在组件中使用
export default defineComponent({
  setup() {
    // 获取上下文（自动类型推断）
    const chartActions = useChartActionsOptional();
    const statisticsActions = useStatisticsActionsOptional();
    const chartData = useChartDataOptional();

    // 使用操作方法
    const handleRefresh = () => {
      chartActions.refreshChart('chartId');
      statisticsActions.refreshStatistics('statsId');
    };

    // 获取状态
    const isLoading = computed(() =>
      chartActions.chartLoadingStates['chartId'] || false
    );

    return { handleRefresh, isLoading };
  }
});
```

## 🔍 故障排查

### 常见问题

1. **上下文注入失败**
   - **现象**：inject 返回 null
   - **原因**：provide 顺序问题（已修复）
   - **解决**：现在使用内置状态管理，无此问题

2. **策略选择失败**
   - **现象**：使用默认Mock策略
   - **原因**：图表配置不匹配任何策略
   - **解决**：检查 `chartConfig.id` 和策略的 `supportedChartTypes`

3. **数据加载失败**
   - **现象**：API调用失败或数据为空
   - **原因**：筛选参数错误或API接口问题
   - **解决**：检查 `getCurrentQueryParams()` 返回值

### 调试技巧

1. **启用策略日志**：
```typescript
// 在策略中添加详细日志
console.log(`🚀 开始加载数据 [${this.strategyType}]: ${chartId}`);
console.log(`📊 API参数:`, finalParams);
console.log(`✅ 数据加载成功:`, transformedData);
```

2. **检查数据源管理器**：
```typescript
const dataSourceManager = useDataSourceManager();
console.log('所有数据源:', dataSourceManager.dataSourceMap);
console.log('元数据:', dataSourceManager.dataSourceMeta);
```

3. **验证上下文状态**：
```typescript
const chartActions = useChartActions();
console.log('加载状态:', chartActions.chartLoadingStates);
console.log('错误状态:', chartActions.chartErrorStates);
```

## 📈 性能优化

### 缓存策略
- **数据源管理器**：自动缓存所有加载的数据
- **策略实例**：工厂模式缓存策略实例，避免重复创建
- **API装饰器**：可扩展支持请求缓存

### 按需加载
- **动态导入**：API方法和工具函数按需导入
- **策略懒加载**：策略类可以按需注册
- **组件懒加载**：图表组件支持懒加载

### 内存管理
- **数据清理**：提供清理缓存的方法
- **状态重置**：支持重置加载状态
- **垃圾回收**：及时清理不再使用的数据

## 🔮 扩展方向

### 1. 增强缓存机制
- 添加过期时间管理
- 支持LRU缓存策略
- 实现持久化缓存

### 2. 完善错误处理
- 统一错误处理机制
- 自动重试策略
- 错误恢复机制

### 3. 性能监控
- 数据加载性能监控
- 策略执行时间统计
- 内存使用监控

### 4. 开发工具
- 策略调试工具
- 数据流可视化
- 性能分析面板

---

**总结**：统计看板统一架构通过上下文系统、策略模式、数据源管理器和API装饰器的完美结合，实现了高度解耦、易扩展、高性能的现代化前端架构。这套架构不仅解决了当前的技术问题，也为未来的功能扩展奠定了坚实的基础。
