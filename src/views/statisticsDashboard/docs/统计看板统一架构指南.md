# 统计看板统一架构指南

## 📋 概述

统计看板采用现代化的Vue 3架构，通过**上下文系统 + 策略模式 + 数据源管理器**实现了完全的组件解耦和统一数据管理。

**核心特点**：
- 🎯 **零透传架构**：0层数据传递，0个不必要emit，0个组件暴露
- 🏗️ **统一上下文系统**：图表和统计数据使用相同的架构模式
- 📊 **策略模式**：自动选择合适的数据加载策略，支持扩展
- 🗄️ **统一数据源管理**：缓存、元数据、类型安全的数据管理
- 🔧 **零手动传参**：API装饰器自动注入筛选参数
- ✨ **内置状态管理**：操作上下文内置状态管理，无需单独状态上下文

## 🏗️ 整体架构

### 架构层次图

```
┌─────────────────────────────────────────────────────────────┐
│                    统计看板主组件                              │
│              UserOperationStatisticsDashboard               │
└─────────────────────┬───────────────────────────────────────┘
                      │ Provide 上下文
┌─────────────────────▼───────────────────────────────────────┐
│                   上下文系统层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ ChartActions    │  │StatisticsActions│  │QueryParams   │ │
│  │ (内置状态管理)   │  │ (内置状态管理)   │  │Context       │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │ 统一数据管理
┌─────────────────────▼───────────────────────────────────────┐
│                  数据源管理器                                 │
│              useDataSourceManager                           │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │  统一数据源映射 (支持图表和统计数据)                        │ │
│  │  - 缓存管理    - 元数据管理    - 类型安全                  │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │ 策略选择
┌─────────────────────▼───────────────────────────────────────┐
│                   策略工厂层                                  │
│  ┌─────────────────┐              ┌─────────────────────────┐ │
│  │ChartDataLoading │              │StatisticsDataLoading    │ │
│  │StrategyFactory  │              │StrategyFactory          │ │
│  └─────────────────┘              └─────────────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │ 具体策略实现
┌─────────────────────▼───────────────────────────────────────┐
│                   策略实现层                                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ClueSourceChart  │  │ClueOverview     │  │DefaultMock   │ │
│  │Strategy         │  │Statistics       │  │Strategy      │ │
│  │                 │  │Strategy         │  │              │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │ API装饰器自动注入参数
┌─────────────────────▼───────────────────────────────────────┐
│                    API调用层                                 │
│              ApiCallDecorator                               │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │  自动注入筛选参数 → API调用 → 数据转换 → 缓存存储          │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 核心系统详解

### 1. 上下文系统 (Context System)

#### 设计原则
- **内置状态管理**：操作上下文内置状态管理，避免注入顺序问题
- **统一架构**：图表和统计数据使用相同的上下文模式
- **类型安全**：完整的TypeScript类型定义

#### 图表操作上下文
```typescript
interface ChartActionContext {
  // 核心操作
  switchChartDataSource: (chartId: string, newDataSource: string, newTitle: string) => void;
  refreshChart: (chartId: string) => void;
  exportChart: (chartId: string, format: string) => void;
  fullscreenChart: (chartId: string) => void;
  
  // 下探功能
  handleDrillDown?: (data: ChartDataItem, chartConfig: any) => Promise<void>;
  resetChartToTopLevel?: (chartConfig: any) => Promise<void>;
  
  // 内置状态管理
  chartLoadingStates: Record<string, boolean>;
  chartErrorStates: Record<string, string | null>;
  setChartLoading: (chartId: string, loading: boolean) => void;
  setChartError: (chartId: string, error: string | null) => void;
}
```

#### 统计数据操作上下文
```typescript
interface StatisticsActionContext {
  // 核心操作
  refreshStatistics: (configId: string) => Promise<void>;
  reloadAllStatistics: () => Promise<void>;
  
  // 内置状态管理
  statisticsLoadingStates: Record<string, boolean>;
  statisticsErrorStates: Record<string, string | null>;
  setStatisticsLoading: (configId: string, loading: boolean) => void;
  setStatisticsError: (configId: string, error: string | null) => void;
}
```

### 2. 数据源管理器 (Data Source Manager)

#### 核心功能
- **统一存储**：图表和统计数据使用同一个数据源映射
- **类型安全**：通过泛型支持不同数据类型
- **元数据管理**：完整的数据源元信息管理
- **缓存机制**：自动缓存和更新数据

#### 使用示例
```typescript
const dataSourceManager = useDataSourceManager();

// 存储图表数据
dataSourceManager.setDataSource<ChartDataItem>('sourceOfClues', chartData, {
  name: '线索来源',
  type: 'chart',
  dataType: 'ChartDataItem'
});

// 存储统计数据
dataSourceManager.setDataSource<StatisticsItem>('clueOverview', statsData, {
  name: '线索总览',
  type: 'statistics', 
  dataType: 'StatisticsItem'
});

// 获取数据（类型安全）
const chartData = dataSourceManager.getDataSource<ChartDataItem>('sourceOfClues');
const statsData = dataSourceManager.getDataSource<StatisticsItem>('clueOverview');
```

### 3. 策略模式 (Strategy Pattern)

#### 图表数据加载策略
```typescript
// 抽象基类
export abstract class BaseChartDataLoadingStrategy {
  protected apiDecorator: IApiCallDecorator;
  protected dataSourceManager: ReturnType<typeof useDataSourceManager>;
  
  // 模板方法：标准数据加载流程
  async loadData(chartConfig: ChartConfig, customParams = {}) {
    // 1. 验证配置
    // 2. 设置加载状态  
    // 3. 通过装饰器调用API
    // 4. 数据转换
    // 5. 存储到数据源管理器
    // 6. 返回结果
  }
}

// 具体策略实现
export class ClueSourceChartStrategy extends BaseChartDataLoadingStrategy {
  readonly strategyType = 'clue-source';
  readonly supportedChartTypes = ['bar', 'column'];
  
  protected async fetchApiData(params: commonQueryParams) {
    const { queryAllClueSource } = await import('../api');
    return await queryAllClueSource(params);
  }
}
```

#### 统计数据加载策略
```typescript
export abstract class BaseStatisticsDataLoadingStrategy {
  protected apiDecorator: IStatisticsApiDecorator;
  protected dataSourceManager: ReturnType<typeof useDataSourceManager>;
  
  async loadStatisticsData(config: StatisticsConfig, params?) {
    // 1. 使用装饰器自动注入参数
    // 2. 转换数据
    // 3. 存储到数据源管理器
    // 4. 返回结果
  }
}
```

### 4. API装饰器 (API Decorator)

#### 自动参数注入
```typescript
export class ApiCallDecorator implements IApiCallDecorator {
  async decorateApiCall<T>(apiCall: (params: commonQueryParams) => Promise<T>, customParams?) {
    // 🔥 自动获取当前筛选参数
    const currentParams = getCurrentQueryParams();
    
    // 合并参数：自定义参数 > 当前筛选参数
    const finalParams = { ...currentParams, ...customParams };
    
    // 调用API
    return await apiCall(finalParams);
  }
}
```

## 🔄 数据流程

### 上下文系统组件数据流转图

```mermaid
graph TB
    subgraph "主组件层"
        A[UserOperationStatisticsDashboard.vue]
    end

    subgraph "上下文提供层 (Provide)"
        B[ChartActions Context]
        C[StatisticsActions Context]
        D[ChartData Context]
        E[StatisticsData Context]
        F[QueryParams Context]
        G[ChartConfig Context]
    end

    subgraph "数据管理层"
        H[DataSourceManager<br/>统一数据源管理器]
        I[TabConfigManager<br/>配置管理器]
    end

    subgraph "策略执行层"
        J[ChartDataLoadingStrategy<br/>图表数据策略]
        K[StatisticsDataLoadingStrategy<br/>统计数据策略]
    end

    subgraph "组件消费层 (Inject)"
        L[ChartGroupContainer.vue]
        M[ChartGroup.vue]
        N[Individual Chart Components]
        O[Statistics Components]
    end

    subgraph "API调用层"
        P[API Decorator<br/>自动参数注入]
        Q[实际API调用]
    end

    %% 主组件提供上下文
    A --> B
    A --> C
    A --> D
    A --> E
    A --> F
    A --> G

    %% 上下文依赖数据管理层
    B --> H
    B --> I
    C --> H
    C --> I
    D --> H
    E --> H
    G --> I

    %% 数据管理层调用策略
    H --> J
    H --> K

    %% 策略调用API
    J --> P
    K --> P
    P --> Q

    %% 组件注入上下文
    L -.-> B
    L -.-> C
    L -.-> D
    L -.-> E
    M -.-> B
    M -.-> D
    M -.-> G
    N -.-> B
    N -.-> D
    O -.-> C
    O -.-> E

    %% 筛选器更新流
    F --> P

    style A fill:#e1f5fe
    style H fill:#f3e5f5
    style P fill:#e8f5e8
    style B fill:#fff3e0
    style C fill:#fff3e0
```

### 详细数据流转说明

#### 1. 上下文提供阶段 (Provide Phase)
```
UserOperationStatisticsDashboard.vue
├── provideChartActions() → ChartActionContext
├── provideStatisticsActions() → StatisticsActionContext
├── provideChartData() → ChartDataContext
├── provideStatisticsData() → StatisticsDataContext
├── provideQueryParams() → QueryParamsContext
└── provideChartConfig() → ChartConfigContext
```

#### 2. 组件消费阶段 (Inject Phase)
```
ChartGroupContainer.vue
├── useStatisticsDataOptional() → 获取统计数据
├── useStatisticsActionsOptional() → 获取统计操作
└── 渲染统计卡片 + ChartGroup组件

ChartGroup.vue
├── useChartDataOptional() → 获取图表数据
├── useChartActionsOptional() → 获取图表操作
├── useChartConfigOptional() → 获取图表配置
└── 渲染具体图表组件

Individual Chart Components
├── useChartDataOptional() → 获取图表数据
├── useChartActionsOptional() → 获取图表操作
└── 渲染图表内容
```

#### 3. 数据加载流程
```
用户操作/页面初始化
    ↓
筛选器更新 → QueryParamsContext
    ↓
组件调用 chartActions.refreshChart() 或 statisticsActions.refreshStatistics()
    ↓
上下文系统调用 DataSourceManager
    ↓
DataSourceManager 选择合适的策略 (ChartDataLoadingStrategy/StatisticsDataLoadingStrategy)
    ↓
策略通过 API Decorator 自动注入筛选参数
    ↓
执行 API 调用 → 数据转换 → 存储到 DataSourceManager
    ↓
上下文系统更新 → 组件自动响应数据变化
```

### 关键数据流特点

#### 🎯 零透传设计
- **无Props传递**：所有数据通过上下文系统传递，组件间无直接数据传递
- **无Emit事件**：所有操作通过上下文方法调用，无需事件冒泡
- **无组件暴露**：组件无需暴露方法，完全通过上下文通信

#### 🔄 自动化流程
- **自动参数注入**：API Decorator 自动获取当前筛选参数
- **自动策略选择**：根据配置自动选择合适的数据加载策略
- **自动状态管理**：加载状态、错误状态自动管理和更新

#### 📊 统一数据管理
- **统一缓存**：所有数据通过 DataSourceManager 统一缓存
- **统一配置**：所有配置通过 TabConfigManager 统一管理
- **统一类型**：完整的 TypeScript 类型支持

### 组件交互时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 筛选器组件
    participant C as ChartGroup组件
    participant CA as ChartActions上下文
    participant DM as DataSourceManager
    participant S as 数据加载策略
    participant API as API调用

    Note over U,API: 页面初始化流程
    U->>F: 页面加载
    F->>CA: 触发初始数据加载
    CA->>DM: 请求图表数据
    DM->>S: 选择加载策略
    S->>API: 调用API (自动注入参数)
    API-->>S: 返回数据
    S->>DM: 存储转换后的数据
    DM-->>CA: 返回数据
    CA-->>C: 上下文数据更新
    C-->>U: 渲染图表

    Note over U,API: 筛选器交互流程
    U->>F: 修改筛选条件
    F->>CA: 调用 refreshChart()
    CA->>DM: 请求刷新数据
    Note over DM: 自动获取最新筛选参数
    DM->>S: 使用新参数重新加载
    S->>API: 调用API (新参数)
    API-->>S: 返回新数据
    S->>DM: 更新缓存数据
    DM-->>CA: 返回新数据
    CA-->>C: 上下文数据更新
    C-->>U: 重新渲染图表

    Note over U,API: 图表操作流程
    U->>C: 点击图表 (下探)
    C->>CA: 调用 handleDrillDown()
    CA->>DM: 请求下探数据
    DM->>S: 选择下探策略
    S->>API: 调用下探API
    API-->>S: 返回下探数据
    S->>DM: 存储下探数据
    DM-->>CA: 返回下探数据
    CA-->>C: 更新图表配置和数据
    C-->>U: 渲染下探图表
```

### 上下文系统优势对比

#### 传统Props传递 vs 上下文系统

```mermaid
graph LR
    subgraph "传统Props传递"
        A1[父组件] --> B1[子组件1]
        A1 --> C1[子组件2]
        B1 --> D1[孙组件1]
        B1 --> E1[孙组件2]
        C1 --> F1[孙组件3]

        style A1 fill:#ffcdd2
        style B1 fill:#ffcdd2
        style C1 fill:#ffcdd2
        style D1 fill:#ffcdd2
        style E1 fill:#ffcdd2
        style F1 fill:#ffcdd2
    end

    subgraph "上下文系统"
        A2[Context Provider]
        B2[组件1] -.-> A2
        C2[组件2] -.-> A2
        D2[组件3] -.-> A2
        E2[组件4] -.-> A2
        F2[组件5] -.-> A2

        style A2 fill:#c8e6c9
        style B2 fill:#c8e6c9
        style C2 fill:#c8e6c9
        style D2 fill:#c8e6c9
        style E2 fill:#c8e6c9
        style F2 fill:#c8e6c9
    end
```

| 特性 | 传统Props传递 | 上下文系统 |
|------|---------------|------------|
| **数据传递层数** | 需要逐层传递 | 直接注入任意层级 |
| **组件耦合度** | 高度耦合 | 完全解耦 |
| **维护成本** | 修改需要改多个组件 | 只需修改上下文 |
| **类型安全** | 容易出错 | 完整类型推断 |
| **代码复用** | 难以复用 | 高度可复用 |
| **测试难度** | 需要模拟整个组件树 | 可单独测试 |

### 实际组件层级数据流示例

```mermaid
graph TD
    subgraph "实际组件结构"
        A[UserOperationStatisticsDashboard.vue<br/>🔥 Provide所有上下文]

        subgraph "Tab容器层"
            B[TabContainer<br/>📋 Tab切换逻辑]
        end

        subgraph "内容渲染层"
            C[ChartGroupContainer.vue<br/>📊 统计数据 + 图表组]
            D[ChartGroup.vue<br/>📈 图表组渲染]
        end

        subgraph "具体图表层"
            E[BarChart.vue<br/>📊 柱状图]
            F[LineChart.vue<br/>📈 折线图]
            G[PieChart.vue<br/>🥧 饼图]
        end

        subgraph "上下文数据流"
            H[ChartActions<br/>🎯 图表操作]
            I[StatisticsActions<br/>📊 统计操作]
            J[DataSourceManager<br/>🗄️ 数据管理]
        end
    end

    %% 组件层级关系
    A --> B
    B --> C
    C --> D
    D --> E
    D --> F
    D --> G

    %% 上下文注入关系 (虚线表示inject)
    C -.-> I
    C -.-> H
    D -.-> H
    E -.-> H
    F -.-> H
    G -.-> H

    %% 上下文内部依赖
    H --> J
    I --> J

    style A fill:#e3f2fd
    style H fill:#fff3e0
    style I fill:#fff3e0
    style J fill:#f3e5f5
```

### 具体数据流转代码示例

#### 1. 主组件提供上下文
```typescript
// UserOperationStatisticsDashboard.vue
export default defineComponent({
  setup() {
    // 🔥 提供所有上下文 - 一次性配置
    const chartActions = provideChartActions();
    const statisticsActions = provideStatisticsActions();
    provideChartData(chartActions);
    provideStatisticsData(statisticsActions);
    provideQueryParams();
    provideChartConfig();

    return { /* 无需返回任何数据给模板 */ };
  }
});
```

#### 2. 组件消费上下文
```typescript
// ChartGroupContainer.vue
export default defineComponent({
  setup() {
    // 🔥 直接注入需要的上下文 - 零配置
    const statisticsData = useStatisticsDataOptional();
    const statisticsActions = useStatisticsActionsOptional();

    // 🔥 使用数据和操作 - 零传参
    const getStatisticsItems = (group) => {
      return statisticsData.getStatisticsData(group.statisticsConfig.id);
    };

    const getLoadingState = (group) => {
      return statisticsActions.statisticsLoadingStates[group.statisticsConfig.id];
    };

    return { getStatisticsItems, getLoadingState };
  }
});
```

#### 3. 图表组件使用上下文
```typescript
// ChartGroup.vue
export default defineComponent({
  setup() {
    // 🔥 注入图表相关上下文
    const chartActions = useChartActionsOptional();
    const chartData = useChartDataOptional();
    const chartConfig = useChartConfigOptional();

    // 🔥 操作方法 - 直接调用
    const handleRefresh = (chartId: string) => {
      chartActions.refreshChart(chartId);
    };

    const handleDrillDown = (data: ChartDataItem, config: any) => {
      chartActions.handleDrillDown?.(data, config);
    };

    // 🔥 数据获取 - 响应式
    const getChartData = (chartId: string) => {
      return chartData.getChartData(chartId);
    };

    return { handleRefresh, handleDrillDown, getChartData };
  }
});
```

### 关键优势
1. **零手动传参**：筛选参数自动注入，无需手动获取和传递
2. **统一架构**：图表和统计数据使用相同的模式
3. **自动缓存**：数据源管理器自动处理缓存
4. **类型安全**：完整的TypeScript支持
5. **易扩展**：新增功能只需添加策略
6. **零层级限制**：任意深度的组件都可以直接注入上下文
7. **完全解耦**：组件间无直接依赖，易于测试和维护

### 状态管理数据流

```mermaid
graph TB
    subgraph "内置状态管理系统"
        subgraph "ChartActions Context"
            CA1[chartLoadingStates<br/>Record&lt;string, boolean&gt;]
            CA2[chartErrorStates<br/>Record&lt;string, string | null&gt;]
            CA3[setChartLoading()<br/>状态设置方法]
            CA4[setChartError()<br/>错误设置方法]
        end

        subgraph "StatisticsActions Context"
            SA1[statisticsLoadingStates<br/>Record&lt;string, boolean&gt;]
            SA2[statisticsErrorStates<br/>Record&lt;string, string | null&gt;]
            SA3[setStatisticsLoading()<br/>状态设置方法]
            SA4[setStatisticsError()<br/>错误设置方法]
        end
    end

    subgraph "组件状态消费"
        C1[ChartGroup.vue<br/>📈 获取图表loading状态]
        C2[ChartGroupContainer.vue<br/>📊 获取统计loading状态]
        C3[Individual Charts<br/>🎯 获取具体图表状态]
    end

    subgraph "状态更新触发"
        T1[数据加载开始<br/>🚀 setLoading(true)]
        T2[数据加载完成<br/>✅ setLoading(false)]
        T3[数据加载失败<br/>❌ setError(message)]
    end

    %% 状态消费关系
    C1 -.-> CA1
    C1 -.-> CA2
    C2 -.-> SA1
    C2 -.-> SA2
    C3 -.-> CA1
    C3 -.-> CA2

    %% 状态更新流程
    T1 --> CA3
    T1 --> SA3
    T2 --> CA3
    T2 --> SA3
    T3 --> CA4
    T3 --> SA4

    style CA1 fill:#e8f5e8
    style CA2 fill:#ffebee
    style SA1 fill:#e8f5e8
    style SA2 fill:#ffebee
```

### 状态管理优势

#### � 内置状态管理 vs 外部状态管理

| 特性 | 外部状态管理 | 内置状态管理 | 优势 |
|------|--------------|--------------|------|
| **注入顺序** | 需要先provide状态 | 无顺序要求 | 解决注入问题 |
| **状态访问** | 需要单独inject | 直接通过操作上下文 | 简化使用 |
| **类型安全** | 可能返回null | 完整类型推断 | 类型安全 |
| **维护成本** | 多个上下文管理 | 单一上下文 | 降低复杂度 |
| **错误处理** | 需要容错处理 | 内置默认值 | 更加稳定 |

#### �📊 状态使用示例

```typescript
// 组件中使用内置状态管理
const chartActions = useChartActionsOptional();

// 🔥 获取loading状态 - 直接访问
const isLoading = computed(() =>
  chartActions.chartLoadingStates['chart-id'] || false
);

// 🔥 获取错误状态 - 类型安全
const errorMessage = computed(() =>
  chartActions.chartErrorStates['chart-id']
);

// 🔥 设置状态 - 内置方法
const handleRefresh = async () => {
  chartActions.setChartLoading('chart-id', true);
  try {
    await chartActions.refreshChart('chart-id');
  } catch (error) {
    chartActions.setChartError('chart-id', error.message);
  } finally {
    chartActions.setChartLoading('chart-id', false);
  }
};
```

## 📊 架构优势

| 特性 | 传统方式 | 统一架构 | 改进 |
|------|----------|----------|------|
| 数据传递 | Props层层传递 | 上下文直接注入 | 0层传递 |
| 状态管理 | 分散在各组件 | 内置状态管理 | 统一管理 |
| 参数传递 | 手动获取传递 | 装饰器自动注入 | 零手动传参 |
| 数据缓存 | 各自管理 | 统一数据源管理器 | 统一缓存 |
| 代码复用 | 重复实现 | 策略模式 | 高度复用 |
| 类型安全 | 部分支持 | 完整TypeScript | 完全类型安全 |
| 上下文注入 | 顺序敏感 | 内置状态管理 | 无顺序问题 |
| 组件耦合 | 高度耦合 | 完全解耦 | 易于测试 |

这个统一架构实现了真正的**零透传、零手动传参、统一管理**，为统计看板提供了强大、灵活、易维护的技术基础。

## 🛠️ 实践指南

### 新增图表数据加载策略

1. **创建策略类**：
```typescript
export class NewChartStrategy extends BaseChartDataLoadingStrategy {
  readonly strategyType = 'new-chart';
  readonly supportedChartTypes = ['line', 'area'];

  protected async fetchApiData(params: commonQueryParams) {
    const { queryNewData } = await import('../api');
    return await queryNewData(params);
  }

  protected async transformData(apiData: any): Promise<ChartDataItem[]> {
    // 数据转换逻辑
    return transformedData;
  }
}
```

2. **注册策略**：
```typescript
ChartDataLoadingStrategyFactory.registerStrategy(new NewChartStrategy());
```

3. **配置图表**：
```typescript
const chartConfig = {
  id: 'newChart',
  type: 'line',
  customProps: {
    loadingStrategy: 'new-chart', // 指定策略
    needsAsyncData: true
  }
};
```

### 新增统计数据策略

1. **创建统计策略**：
```typescript
export class NewStatisticsStrategy extends BaseStatisticsDataLoadingStrategy {
  readonly strategyType = 'new-statistics';
  readonly supportedDataSources = ['newStats'];

  protected async fetchApiData(params: commonQueryParams) {
    const { queryNewStats } = await import('../api');
    return await queryNewStats(params);
  }

  protected async transformData(apiData: any, config: StatisticsConfig) {
    // 转换为 StatisticsItem[]
    return transformedStats;
  }
}
```

2. **注册策略**：
```typescript
StatisticsDataLoadingStrategyFactory.registerStrategy(new NewStatisticsStrategy());
```

### 组件中使用上下文

```typescript
// 在组件中使用
export default defineComponent({
  setup() {
    // 获取上下文（自动类型推断）
    const chartActions = useChartActionsOptional();
    const statisticsActions = useStatisticsActionsOptional();
    const chartData = useChartDataOptional();

    // 使用操作方法
    const handleRefresh = () => {
      chartActions.refreshChart('chartId');
      statisticsActions.refreshStatistics('statsId');
    };

    // 获取状态
    const isLoading = computed(() =>
      chartActions.chartLoadingStates['chartId'] || false
    );

    return { handleRefresh, isLoading };
  }
});
```

## 🔍 故障排查

### 常见问题

1. **上下文注入失败**
   - **现象**：inject 返回 null
   - **原因**：provide 顺序问题（已修复）
   - **解决**：现在使用内置状态管理，无此问题

2. **策略选择失败**
   - **现象**：使用默认Mock策略
   - **原因**：图表配置不匹配任何策略
   - **解决**：检查 `chartConfig.id` 和策略的 `supportedChartTypes`

3. **数据加载失败**
   - **现象**：API调用失败或数据为空
   - **原因**：筛选参数错误或API接口问题
   - **解决**：检查 `getCurrentQueryParams()` 返回值

### 调试技巧

1. **启用策略日志**：
```typescript
// 在策略中添加详细日志
console.log(`🚀 开始加载数据 [${this.strategyType}]: ${chartId}`);
console.log(`📊 API参数:`, finalParams);
console.log(`✅ 数据加载成功:`, transformedData);
```

2. **检查数据源管理器**：
```typescript
const dataSourceManager = useDataSourceManager();
console.log('所有数据源:', dataSourceManager.dataSourceMap);
console.log('元数据:', dataSourceManager.dataSourceMeta);
```

3. **验证上下文状态**：
```typescript
const chartActions = useChartActions();
console.log('加载状态:', chartActions.chartLoadingStates);
console.log('错误状态:', chartActions.chartErrorStates);
```

## 📈 性能优化

### 缓存策略
- **数据源管理器**：自动缓存所有加载的数据
- **策略实例**：工厂模式缓存策略实例，避免重复创建
- **API装饰器**：可扩展支持请求缓存

### 按需加载
- **动态导入**：API方法和工具函数按需导入
- **策略懒加载**：策略类可以按需注册
- **组件懒加载**：图表组件支持懒加载

### 内存管理
- **数据清理**：提供清理缓存的方法
- **状态重置**：支持重置加载状态
- **垃圾回收**：及时清理不再使用的数据

## 🔮 扩展方向

### 1. 增强缓存机制
- 添加过期时间管理
- 支持LRU缓存策略
- 实现持久化缓存

### 2. 完善错误处理
- 统一错误处理机制
- 自动重试策略
- 错误恢复机制

### 3. 性能监控
- 数据加载性能监控
- 策略执行时间统计
- 内存使用监控

### 4. 开发工具
- 策略调试工具
- 数据流可视化
- 性能分析面板

---

**总结**：统计看板统一架构通过上下文系统、策略模式、数据源管理器和API装饰器的完美结合，实现了高度解耦、易扩展、高性能的现代化前端架构。这套架构不仅解决了当前的技术问题，也为未来的功能扩展奠定了坚实的基础。
